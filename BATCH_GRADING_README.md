# Batch Document Grading Optimization

## Overview

This implementation introduces **batch document grading** to significantly reduce the number of LLM calls required for document relevance evaluation in the subquery agent flow.

## Performance Improvement

**Before (Individual Grading):**
- 1 LLM call per document
- For N documents = N LLM calls

**After (Batch Grading):**
- Multiple documents graded in a single LLM call
- For N documents with batch size B = ⌈N/B⌉ LLM calls
- **Typical reduction: 60-90% fewer LLM calls**

## Example Performance Gains

| Documents | Batch Size | Individual Calls | Batch Calls | Reduction |
|-----------|------------|------------------|-------------|-----------|
| 10        | 3          | 10               | 4           | 60%       |
| 15        | 5          | 15               | 3           | 80%       |
| 8         | 8          | 8                | 1           | 87.5%     |
| 20        | 10         | 20               | 2           | 90%       |

## Configuration

### Enable/Disable Batch Grading

```python
config = {
    'batch_grading_enabled': True,  # Default: True
}
```

### Batch Size Configuration

```python
config = {
    'batch_grading_size': 8,  # Default: 8 documents per batch
}
```

### Recommended Batch Sizes

- **Small batches (3-5)**: Better for complex documents or when LLM context is limited
- **Medium batches (6-10)**: Good balance of performance and accuracy (recommended)
- **Large batches (10+)**: Maximum performance but may reduce grading accuracy

## Implementation Details

### New Pydantic Models

```python
class DocumentGrade(BaseModel):
    """Grade for a single document with document identifier."""
    document_index: int = Field(description="Index of the document in the batch (0-based)")
    reasoning: str = Field(description="Reasoning behind the score, one sentence.")
    score: int = Field(description="Relevance score from 0 to 10")

class BatchGrade(BaseModel):
    """Batch grading response for multiple documents."""
    document_grades: List[DocumentGrade] = Field(description="List of grades for each document in the batch")
```

### New Functions

- `batch_grade_documents_fn()`: Creates a batch document grading function using async tasks
- Enhanced `subquery_grading_fn()`: Now supports both batch and individual grading
- Enhanced `setup_document_processing()`: Automatically chooses grading method based on config

### Async Implementation

The batch grading follows the same async pattern as `grade_each_fn()`:

```python
# Prepare batches and async tasks
async_tasks = []
batch_metadata = []

for batch_start in range(0, len(documents), batch_size):
    # Create batch and add to async tasks
    async_tasks.append(retrieval_grader.ainvoke(invoke_dict))
    batch_metadata.append(batch_info)

# Execute all batch grading tasks concurrently
batch_results = await asyncio.gather(*async_tasks)
```

This provides **dual performance benefits**:
1. **Fewer LLM calls**: Batch processing reduces total API calls
2. **Concurrent execution**: Multiple batches processed simultaneously

## Usage in Different Contexts

### Subquery Agent

The subquery agent automatically uses batch grading when enabled:

```python
# In subquery_agent.py
use_batch_grading = config.get('batch_grading_enabled', True)
if use_batch_grading:
    document_grader = agents.batch_grade_documents_fn(llm, config)
else:
    document_grader = agents.grade_each_fn(llm, config)
```

### Standard Agent

The standard agent workflow also supports batch grading:

```python
# In agents.py setup_document_processing()
use_batch_grading = config.get('batch_grading_enabled', True)
if use_batch_grading:
    workflow.add_node(PATH_GRADE_DOCS, batch_grade_documents_fn(cheap_fast_llm, config))
else:
    workflow.add_node(PATH_GRADE_DOCS, grade_each_fn(cheap_fast_llm, config))
```

## Error Handling

The batch grading implementation includes robust error handling:

- **Fallback mechanism**: If batch grading fails, it falls back to a default scoring
- **Index validation**: Ensures document indices in responses are valid
- **Graceful degradation**: System continues to work even if batch grading encounters issues

## Backward Compatibility

- **Fully backward compatible**: Existing code continues to work without changes
- **Configuration-driven**: Can be enabled/disabled via config
- **Same output format**: Returns the same document structure as individual grading

## Best Practices

1. **Start with default settings**: `batch_grading_enabled: True` and `batch_grading_size: 8`
2. **Monitor performance**: Track LLM call reduction and grading accuracy
3. **Adjust batch size**: Based on your document complexity and LLM capabilities
4. **Test thoroughly**: Verify grading quality remains consistent with your use case

## Monitoring and Debugging

The implementation includes detailed logging:

```
---BATCH DOCUMENT GRADING: Processing 10 documents in batches of 8---
---BATCH GRADE: DOC RELEVANT [name=doc1.txt page=1 score=7 reasoning=Contains relevant information]---
---BATCH GRADE: DOC NOT RELEVANT [name=doc2.txt page=1 score=3 reasoning=Off-topic content]---
---BATCH GRADING COMPLETE: 6 relevant documents out of 10 total---
```

## Future Enhancements

Potential improvements for future versions:

1. **Dynamic batch sizing**: Automatically adjust batch size based on document length
2. **Parallel batch processing**: Process multiple batches concurrently
3. **Smart batching**: Group similar documents together for better context
4. **Adaptive thresholds**: Adjust scoring thresholds based on batch performance

## Testing

Run the included tests to verify the implementation:

```bash
python simple_batch_test.py
```

This validates:
- Pydantic model functionality
- Batch size calculations
- Configuration options
- Performance improvement calculations
