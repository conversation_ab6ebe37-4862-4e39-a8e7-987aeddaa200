api:
  Image:
    Name: kuberlab/ira-chat-api
    Tag: latest
  baseUrl: https://hotline.kibernetika.io
  db:
    enabled: true
    host: ira-chat-db
    user: postgres
    password: Privet1961sneg
    dbname: ira
  dns:
    - hotline.kibernetika.io
  redirectDomain: ''
  replicas: 3
  envPrefix: 'dev'
  envVars:
    - name: VECTORSTORE
      value: qdrant
    - name: VECTORSTORE_ON_DISK
      value: "true"
    - name: LANGFUSE_SECRET_KEY
      value: ""
    - name: LANGFUSE_PUBLIC_KEY
      value: ""
wildcard:
  enabled: true
  dns: '*.hotline.kibernetika.io'
  serviceAccountJson: ''
  googleProject: ''
resources:
   limits:
     memory: 4Gi
   requests:
     memory: 1Gi

files:
  Image:
    Name: kuberlab/chat-file-api
    Tag: latest

converter:
  Image:
    Name: kuberlab/doc-converter-go
    Tag: latest

qdrant:
  nameOverride: "qdrant"
  fullnameOverride: "qdrant"
  resources:
    limits:
  #   cpu: 100m
      memory: 8Gi
    requests:
  #   cpu: 100m
      memory: 2Gi
  persistence:
    size: 20G

  image:
    repository: docker.io/qdrant/qdrant
    pullPolicy: IfNotPresent
    tag: "v1.10.1"

  # api key for authentication at qdrant
  # false: no api key will be configured
  # true: an api key will be auto-generated
  # string: the given string will be set as an apikey
  apiKey: false

crypto: jS2fKKFSlfco3mxLdfckd0ew
credentials:
  admins: W10K
  smtp: e30=
  google: e30=
persistence:
  db:
    size: 30G
    storageClass: standard
  files:
    size: 50G
    storageClass: standard
ui:
  Image:
    Name: kuberlab/ira-chat-ui
    Tag: latest
  replicas: 1

nodeSelector: {}
tolerations: []
