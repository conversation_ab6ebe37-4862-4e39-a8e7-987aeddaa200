apiVersion: apps/v1
kind: Deployment
metadata:
  name: ira-chat-api
spec:
  selector:
    matchLabels:
      app: ira-chat
      component: ira-chat-api
  replicas: {{ .Values.api.replicas }}
  template:
    metadata:
      labels:
        app: ira-chat
        component: ira-chat-api
    spec:
      containers:
        - name:  ira-chat-api
          image: "{{ printf "%s:%s" .Values.api.Image.Name .Values.api.Image.Tag}}"
          readinessProbe:
            httpGet:
              path: /health
              port: 8083
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: PGHOST
            {{- if .Values.api.db.enabled }}
              value: ira-chat-db
            {{- else }}
              value: {{ .Values.api.db.host }}
            {{- end }}
            - name: PGUSER
              value: {{ .Values.api.db.user }}
            - name: PGPASSWORD
              value: {{ .Values.api.db.password }}
            - name: PGDATABASE
              value: {{ .Values.api.db.dbname }}
            - name: BASE_URL
              value: {{ .Values.api.baseUrl }}
            - name: FILE_API_URL
              value: http://chat-file-api:8084
            - name: CONVERTER_API_URL
              value: http://doc-converter:8085
            - name: ENV_PREFIX
              value: {{ .Values.api.envPrefix }}
            - name: CREDENTIALS
              value: "/go/credentials"
            - name: REDIS_HOST
              value: "redis"
            {{- range .Values.api.envVars }}
            - name: {{ .name }}
              value: '{{ .value }}'
            {{- end }}
            - name: BLOWFISH_SALT
              valueFrom:
                secretKeyRef:
                  name: crypto
                  key: blowfish-salt
          ports:
            - containerPort: 8083
              name: http
          volumeMounts:
            - mountPath: /go/credentials
              name: credentials
            #- mountPath: /data
            #  name: data
      terminationGracePeriodSeconds: 90
      volumes:
        #- name: data
        #  persistentVolumeClaim:
        #    claimName: ira-chat-data
        - name: credentials
          secret:
            secretName: ira-chat-credentials
            items:
              {{- range $key, $value := .Values.credentials }}
              - key: {{ $key }}.json
                path: {{ $key }}.json
              {{- end }}
