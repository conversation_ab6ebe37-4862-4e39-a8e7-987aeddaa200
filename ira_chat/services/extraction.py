import json
import logging
from typing import Literal

from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.chains.retrieval import create_retrieval_chain
from langchain.chat_models.base import BaseChatModel
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.vectorstores import VectorStore
from pydantic import BaseModel

from ira_chat.db import api as db_api
from ira_chat.db import models
from ira_chat.utils.json_utils import extract_json

logger = logging.getLogger(__name__)

JSON_FORMAT_INSTRUCTIONS = """The output should be formatted as a JSON instance that conforms to the JSON schema below.
If a value can not be determined, then put there null.
Do not fill in the placeholders where specific data is not available.

As an example, for the schema 
{
  "properties": {
    "foo": {
      "title": "Foo",
      "description": "a list of strings",
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "bar": {
      "title": "Bar",
      "description": "a string",
      "type": ["string", "null"]
    }
  },
  "required": [
    "foo"
  ]
}
the object 
{"foo": ["ber", "baz"], "bar": null} 
is a well-formatted instance of the schema. The object 
{"properties": {"foo": ["bar", "baz"], "bar": 123}} 
is not well-formatted.

Here is the output schema:
```
SCHEMA_PLACEHOLDER
```"""


async def process_extractor_request(
    llm: BaseChatModel, vectorstore: VectorStore,
    ext: models.Extractor,
    ext_item: models.ExtractorItem,
    out: models.ExtractorItemOutput,
    access_type='session'
):
    try:
        ext_config = ext.get_config()
        output = await _process_extractor(
            llm, vectorstore, ext_config, ext_item.input, input_type='text', access_type=access_type
        )
    except Exception as e:
        msg = f'{e.__class__.__name__}: {str(e)}'
        updated = {
            'output': msg,
            'status': models.STATUS_ERROR,
        }
        logger.exception(msg)
    else:
        updated = {
            'status': models.STATUS_SUCCESS,
            'output': output
        }
    await db_api.update_extractor_item_output(out, updated)
    await db_api.update_extractor_item(ext_item, {'status': updated['status']})


async def process_extractor(
    llm: BaseChatModel, vectorstore: VectorStore,
    ext: models.Extractor,
    ext_result: models.ExtractorResult,
    access_type='session'
):
    try:
        ext_config = ext.get_config()
        output = await _process_extractor(
            llm, vectorstore, ext_config, input=ext.schema, input_type='schema', access_type=access_type
        )
    except Exception as e:
        msg = f'{e.__class__.__name__}: {str(e)}'
        status = models.STATUS_ERROR
        updated = {'status': {'status': status, 'message': msg}}
        logger.exception(msg)
    else:
        status = models.STATUS_SUCCESS
        updated = {
            'status': {'status': status, 'message': None},
            'output': json.dumps(output) if not isinstance(output, str) else output,
        }
    await db_api.update_extractor_result(ext_result, updated)
    await db_api.update_extractor(
        ext,
        {'status': updated['status'], 'has_updates': status != models.STATUS_SUCCESS}
    )


async def _process_extractor(
    llm: BaseChatModel, vectorstore: VectorStore,
    ext_config: dict, input: str = None,
    input_type: Literal['text', 'schema'] = 'schema',
    access_type='session'
) -> str:
    kwargs = {
        'search_type': 'similarity',
        'search_kwargs': {'k': ext_config.get('search_k') or 64},
    }
    retriever = vectorstore.as_retriever(**kwargs)

    system_prompt = (
        "The context:"
        "\n\n{context}"
    )
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system_prompt),
            ("human", "{input}"),
        ]
    )

    is_schema = input_type == 'schema'
    if is_schema:
        # Remove extraneous fields.
        reduced_schema = json.loads(input)
        if "title" in reduced_schema:
            del reduced_schema["title"]
        if "type" in reduced_schema:
            del reduced_schema["type"]
        input = JSON_FORMAT_INSTRUCTIONS.replace('SCHEMA_PLACEHOLDER', json.dumps(reduced_schema, indent=2))

    # Can get JSON object right away
    # output_parsers.PydanticOutputParser()
    # output_parser = output_parsers.JsonOutputParser()
    # output_parser = None
    document_chain = create_stuff_documents_chain(
        llm,
        prompt,
        document_prompt=PromptTemplate.from_template("{source}:\n{page_content}"),
        output_parser=None
    )
    chain = create_retrieval_chain(retriever, document_chain)

    output = await chain.ainvoke({"input": input})
    logger.info(f"[EXTRACTOR] Output: {output['answer']}")

    if is_schema:
        try:
            return JsonOutputParser().parse(output['answer'])
        except Exception as e:
            result = extract_json(output['answer'])
            if not result:
                raise

            return result
    else:
        return output['answer']


def build_model_from_schema(json_schema: dict) -> BaseModel:
    pass
