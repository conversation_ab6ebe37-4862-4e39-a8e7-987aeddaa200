import collections
import logging

from langchain_core.language_models import BaseChatModel
from langchain_openai import AzureChatOpenAI, ChatOpenAI, OpenAIEmbeddings, AzureOpenAIEmbeddings

from ira_chat.services.llms import base, openai_llm
from ira_chat.utils.utils import llm_vision_model_by_llm_type

logger = logging.getLogger(__name__)


class AzureOpenAI(openai_llm.OpenAI):
    def __init__(self, org_config: dict, params: dict, org_id: int, workspace_id: int = None):
        self.default_api_version = "2024-08-01-preview"
        super().__init__(org_config, params, org_id, workspace_id)

    def _init_llm(self, params: dict):
        if self.need_vision:
            if params.get('vision_model'):
                params['model_name'] = params.get('vision_model')
            else:
                params['model_name'] = llm_vision_model_by_llm_type(self.llm_type)

        logger.info("INIT AZURE OPENAI")
        llm = AzureChatOpenAI(
            api_key=self.llm_params.get('api_key'),
            azure_endpoint=self.llm_params.get('api_endpoint'),
            api_version=self.llm_params.get('api_version') or self.default_api_version,
            azure_deployment=params['model_name'],
            model_name=params['model_name'],
            timeout=int(params['request_timeout']),
            max_retries=int(params.get('max_retries', self.default_max_retries)),
            temperature=float(params.get('temperature') or self.default_temperature),
            max_tokens=params.get('max_output_tokens'),
            callbacks=self.get_callbacks(),
            top_p=float(params.get('top_p', 1.0)),
            frequency_penalty=float(params.get('frequency_penalty', 0.0)),
            presence_penalty=float(params.get('presence_penalty', 0.0)),
        )

        return llm

    @staticmethod
    def _preprocess_params(params: dict):
        params = super(AzureOpenAI, AzureOpenAI)._preprocess_params(params)
        # Skip for embeddings
        if params.get('model_name', '')[:2] in ['o1', 'o3']:
            params['temperature'] = 1
            params['top_p'] = 1
            params['frequency_penalty'] = 0
            params['presence_penalty'] = 0
        # if params.get('model_name', '')[:2] in ['o1']:
        #     params['disabled_params'] = {"parallel_tool_calls": None}
        return params

    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        stream_callback = base.StreamingCallbackHandler(params['stream_queue'], params['stream_event'])
        llm = self.init_llm(params)
        llm.streaming = True
        llm.callbacks.append(stream_callback)

        return llm

    def init_embeddings(self, override: dict = None):
        logger.info("INIT EMBEDDING AZURE OPENAI")

        params = self.get_embedding_model_params(self.org_config)
        if override:
            params.update(override)

        default_model = 'text-embedding-ada-002'
        return AzureOpenAIEmbeddings(
            api_key=params.get('api_key'),
            azure_endpoint=params.get('api_endpoint'),
            azure_deployment=params.get('embedding_model') or default_model,
            api_version=params.get('api_version') or self.default_api_version,
            model=params.get('embedding_model') or default_model,
            # text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002
        )

    @staticmethod
    def available_models():
        return {
            'models': [
                'gpt-4o',
                'gpt-4o-mini',
                'o3-mini',
            ],
            'vision_models': [
                'gpt-4o',
                'gpt-4o-mini',
            ],
            'embedding_models': [
                'text-embedding-3-small',
                'text-embedding-3-large',
                'text-embedding-ada-002',
            ],
        }

    @staticmethod
    def prices_per_model() -> dict:
        # openai models, price for 1M tokens
        prices = collections.defaultdict(lambda: [10, 30])
        prices.update({
            'gpt-4o': [2.75, 11],
            'gpt-4o-2024-08-06': [2.75, 11],
            'gpt-4o-mini': [0.165, 0.66],
            'gpt-4o-mini-2024-07-18': [0.165, 0.66],
            'o1': [15, 60],
            'o1-2024-12-17': [15, 60],
            'o1-preview': [15, 60],
            'o1-preview-2024-09-12': [15, 60],
            'o1-mini': [1.1, 4.4],
            'o1-mini-2024-09-12': [1.1, 4.4],
            'o3-mini': [1.1, 4.4],
            'o3-mini-2025-01-31': [1.1, 4.4],
        })
        return prices

    @classmethod
    def compute_cost(cls, messages, output, input_tokens, output_tokens, model_name):
        default_prices = [10, 30]
        prices = cls.prices_per_model().get(model_name)
        if not prices:
            # search by prefix
            for key, value in cls.prices_per_model().items():
                if model_name.startswith(key):
                    prices = value
                    break
        if not prices:
            prices = default_prices

        multiplier = 1e6
        price_input, price_output = prices
        total_cost = input_tokens * price_input / multiplier + output_tokens * price_output / multiplier
        return total_cost
