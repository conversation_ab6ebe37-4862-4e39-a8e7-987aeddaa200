import asyncio
import logging
import math
import os
from typing import List

from langchain_core.embeddings import Embeddings
from langchain_core.language_models import BaseChatModel
from langchain_google_genai import ChatGoogleGenerativeAI
from ragas import RunConfig
from ragas import evaluate
from ragas.dataset_schema import EvaluationDataset
from ragas.metrics import faithfulness, answer_relevancy, ContextUtilization
from ragas.metrics.base import MetricWithLLM

from ira_chat.services.llms.base import get_trace_callbacks

logger = logging.getLogger(__name__)


async def run_evaluation(
    org_id, ws_id: int, app_type: str, app_id: int, object_id: int,
    llm: BaseChatModel, embeddings: Embeddings, input: str,
    output: str, contexts: list[str], insert_all: float = None,
) -> list[dict]:
    def build_metric_dict(name, value, model_name=None):
        return {
            'value': value,
            'type': name,
            'app_id': app_id,
            'app_type': app_type,
            'object_id': object_id,
            'workspace_id': ws_id,
            'org_id': org_id,
            'extra': {"model_name": model_name}
        }
    # Take top-8
    contexts = contexts[:4] or ['No context']

    dataset = EvaluationDataset.from_list([
        {'user_input': input, 'response': output, 'retrieved_contexts': contexts}
    ])
    # llm = llm_utils.set_cheapest_model(llm)
    # google:
    # metric.llm.langchain_llm.model
    # openai:
    # metric.llm.langchain_llm.model_name
    model_name = ''
    if hasattr(llm, 'model'):
        model_name = llm.model
    elif hasattr(llm, 'model_name'):
        model_name = llm.model_name

    # metric_params = {'llm': LangchainLLMWrapper(llm)}
    # langchain_embeddings = LangchainEmbeddingsWrapper(embeddings)

    metrics_to_evaluate = [
        faithfulness,
        ContextUtilization(),
        answer_relevancy
    ]

    if insert_all is not None:
        return [build_metric_dict(m.name, insert_all, model_name) for m in metrics_to_evaluate]

    # noinspection PyShadowingNames
    async def evaluate_and_save(
        dataset, metrics: List[MetricWithLLM],
        llm: BaseChatModel, embeddings: Embeddings
    ):
        # asyncio_loop = os.getenv('ASYNCIO_LOOP', 'asyncio')
        # if asyncio_loop == 'uvloop':
        #     if isinstance(llm, ChatGoogleGenerativeAI):
        #         logger.warning("Evaluation metrics for Gemini model are turned off due to the error.")
        #         return []

        result = await evaluate(
            dataset,
            metrics,
            llm=llm,
            embeddings=embeddings,
            show_progress=False,
            run_config=RunConfig(max_workers=4),
            callbacks=get_trace_callbacks(),
        )
        metric_map = {}
        for m in metrics:
            try:
                pre_result = result[m.name]
                value = float(pre_result[0] if isinstance(pre_result, list) else pre_result)
            except Exception as e:
                logger.error(f'{e.__class__.__name__}: {str(e)}')
                value = 0

            if math.isnan(value):
                value = 0

            metric_map[m.name] = value

        def create_metrics(metric_dict):
            metric_dicts = []
            for name, metric_value in metric_dict.items():
                metric_dicts.append(build_metric_dict(name, metric_value, model_name))
            return metric_dicts

        metric_objs = create_metrics(metric_map)
        return metric_objs

    # loop = asyncio.get_event_loop()
    # metric_objs = await loop.run_in_executor(
    #     None, evaluate_and_save, dataset, metrics_to_evaluate, llm, embeddings,
    # )
    metric_objs = await evaluate_and_save(dataset, metrics_to_evaluate, llm, embeddings)
    # for metric in metrics_to_evaluate:
    #     asyncio.create_task(evaluate_and_save(dataset, metric, ws_id, app_type, app_id, object_id))
    return metric_objs



