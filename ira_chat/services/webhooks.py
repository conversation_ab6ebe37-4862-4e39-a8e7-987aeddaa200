from typing import Union, Optional

import asyncio
import base64
import hmac
import json
import hashlib
import httpx
import logging
import time

from pydantic import BaseModel

from ira_chat.db import models
from ira_chat.db import api as db_api

kibernetika_header = 'X-Kibernetika-Hmac-Sha256'
client = httpx.AsyncClient(
    timeout=httpx.Timeout(timeout=60.0)
)
logger = logging.getLogger(__name__)

WEBHOOK_MESSAGE = 'chat_message'
WEBHOOK_FILE = 'workspace_file'
_hook_structure = {
    "type": WEBHOOK_MESSAGE,
    "status": models.STATUS_SUCCESS,
    "data": {}
}


class WebhookOrgConfig(BaseModel):
    all: Optional[bool] = True


class WebhookWorkspaceConfig(BaseModel):
    all: Optional[bool] = True
    ids: Optional[list[int]] = None
    application_types: Optional[list[str]] = None


class WebhookDatasetConfig(BaseModel):
    all: Optional[bool] = True


class WebhookConfig(BaseModel):
    organization: Optional[WebhookOrgConfig] = WebhookOrgConfig(all=True)
    workspace: Optional[WebhookWorkspaceConfig] = WebhookWorkspaceConfig(all=True)
    dataset: Optional[WebhookDatasetConfig] = WebhookDatasetConfig(all=True)


def check_webhook_sender(
    webhook: models.OrganizationWebhook,
    sender: Union[models.Workspace, models.Organization],
) -> bool:
    config = WebhookConfig(**webhook.config)

    if isinstance(sender, models.Organization):
        return config.organization.all
    elif isinstance(sender, models.Workspace):
        if config.workspace.all:
            return True
        else:
            return config.workspace.ids and sender.id in config.workspace.ids
    else:
        return False


async def send_webhook(
    webhook: models.OrganizationWebhook,
    sender: Union[models.Workspace, models.Organization],
    data: Union[dict, list]
):
    t1 = time.time()
    to_send = check_webhook_sender(webhook, sender)
    if not to_send:
        return
    logger.info(f'[WEBHOOK] Sending webhook [url={webhook.url}, description={webhook.description}]; checked in {(time.time() - t1) * 1000:.2f}ms.')
    t = time.time()
    headers, data_str, status, resp_headers, content, err = await _retry_send_webhook(
        webhook,
        data,
        delay=1,
        jitter=1,
        tries=7,
    )
    if not err:
        logger.info(f'[WEBHOOK] Sent webhook [url={webhook.url}, description={webhook.description}] status={status} in {(time.time() - t) * 1000:.2f}ms.')
    else:
        logger.error(f'[WEBHOOK] Failed send webhook [url={webhook.url}, description={webhook.description}]: {err}')

    await db_api.create_org_webhook_item({
        'webhook_id': webhook.id,
        'headers': json.dumps(headers),
        'payload': data_str,
        'response': content,
        'status_code': status,
        'response_headers': json.dumps(resp_headers),
        'error': err,
    })
    await db_api.keep_n_webhook_items(webhook.id, 10)


async def _retry_send_webhook(webhook, data, delay=0, tries=1, backoff=1, jitter=0, max_delay=256):
    _tries, _delay = tries, delay
    err = None
    headers, data_str, status = {}, "", 0
    while _tries:
        headers, data_str, status, resp_headers, content, err = await _send_webhook(webhook, data)
        if not err:
            return headers, data_str, status, resp_headers, content, err
        else:
            logger.error(f'[WEBHOOK] Retry failed webhook: {str(err)}')
            _tries -= 1

            await asyncio.sleep(_delay)
            _delay *= backoff
            _delay += jitter
            _delay = max_delay if _delay > max_delay else _delay

    return headers, data_str, status, {}, "", err


async def _send_webhook(webhook: models.OrganizationWebhook, data: Union[dict, list]):
    headers = webhook.get_headers()

    data_str = json.dumps(data)
    headers[kibernetika_header] = hmac_sha256(webhook.key, data_str)
    headers['Content-Type'] = 'application/json'
    resp = None
    err = None
    try:
        resp = await client.post(webhook.url, content=data_str, headers=httpx.Headers(headers, encoding='utf-8'))
        if resp.status_code >= 400:
            err = f'{resp.status_code}: {resp.content.decode()}'
    except Exception as e:
        err = f'{e.__class__.__name__}: {str(e)}'

    status = resp.status_code if resp else 0
    content = resp.content.decode() if resp else None
    resp_headers = {k: v for k, v in resp.headers.items()} if resp else None

    return headers, data_str, status, resp_headers, content, err


def hmac_sha256(key, message):
    return base64.standard_b64encode(
        hmac.new(
            key.encode(),
            message.encode(),
            hashlib.sha256
        ).digest()
    ).decode()
