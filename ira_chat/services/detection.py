import asyncio
import base64
import functools
import io
import logging
import mimetypes
import os
import shutil
from typing import Union, Any, Literal

import numpy as np
import pdf2image
import retry
from PIL import Image
from PIL import ImageOps
from fastapi import UploadFile, HTTPException
from langchain.chat_models.base import BaseChatModel
from langchain_core.documents import Document
from langchain_core.messages import HumanMessage, SystemMessage
from typing_extensions import AsyncGenerator

from ira_chat.config.shared_config import SharedConfig
from ira_chat.db import api as db_api
from ira_chat.db import models
from ira_chat.services import tesseract, file_formats
from ira_chat.services.detections import posology, barcode
from ira_chat.services.file_processor import get_model_context_limit, count_tokens, split_document
from ira_chat.services.llms.base import write_metrics
from ira_chat.utils import llm_utils, retry_utils
from ira_chat.utils.http_utils import download_content
from ira_chat.utils.json_utils import extract_json

SMALL_SLEEP = 0.01
logger = logging.getLogger(__name__)
_prescription_prompt_template = """
Extract all fields from the image.
---
- The output should be in json format enclosed within ```
- The output should have these json fields:

"description": "",
"patient": "",
"doctor": "",
"prescription": [{{"medicament": "", "dosage": ""}}],
"date": ""
"""

DEFAULT_SYSTEM_PROMPT = """
Extract all fields manually. 
- Detect and recognize the handwritten medicament if present and write in the unknown field.
- If there is a double line square with a number inside present at the bottom of the document, recognize the number.
- The output should be in json format.
- The output should have these json fields:
- everything other field should be placed in separate "unknown" section 
- if prescription is divided into sections labeled "AFFECTION EXONERANTE" and "MALADIES INTERCURRENTES" put the section name in the "BiZone" field
- if there is any additional text generated after the json formatting put it in the extra field
- Do not output anything outside of JSON

"description": "",
"medic": {"RPPS": "", "AM": "", "Nom": "", "Prenom": "","Tel": "","Title": "","Speciality": "","Address": "",},
"date": "",
"patient": {"NomPrenom": "","Sex": "","Born": "","Age": "","Weight": "","Creatinine": "","Height": "","Square": ""},
"prescription": [{"Medicament": "", "Poso": "","Voie":"","BiZone":""}],
"double_line_square": "",
"unknown": [{"field}:""}],
"extra":""
"""
DEFAULT_PROMPT_CONFIG = {
    'system_prompt': DEFAULT_SYSTEM_PROMPT,
}


prompts = {
    models.DETECTION_PRESCRIPTION: _prescription_prompt_template,
}

max_sizes = {
    'google': 4096000,     #  4 MB
    'openai': 20480000,    # 20 MB
    'anthropic': 5242880,  #  5 MB
}
tiff_sigs = ['49492a00']
png_sigs = ['89504e47']
jpg_sigs = ['ffd8ffe0', 'ffd8ffe1', 'ffd8ffe2', 'ffd8ffee']
pdf_sigs = ['25504446']
sigs = {
    '.tiff': tiff_sigs,
    '.tif': tiff_sigs,
    '.png': png_sigs,
    '.jpg': jpg_sigs,
    '.jpeg': jpg_sigs,
}
image_extensions = list(sigs.keys())
sigs['.pdf'] = pdf_sigs
to_recognition = set(list(sigs.keys()))


def load_image(img_data: Image.Image | bytes | np.ndarray) -> Image.Image:
    if isinstance(img_data, Image.Image):
        return img_data
    if isinstance(img_data, np.ndarray):
        res = Image.fromarray(img_data, mode="RGB")
        del img_data
        return res
    res = Image.open(io.BytesIO(img_data))
    del img_data
    return res


async def transform_image(img_data, max_size=None, transforms: list = None, reduce_transforms: list = None):
    loop = asyncio.get_event_loop()
    for transform in transforms:
        img_data, ext = await loop.run_in_executor(None, transform, img_data)

    if not max_size:
        return img_data, None

    if await check_base64_size(img_data, max_size):
        return img_data, None

    img_data_old = img_data
    for transform in reduce_transforms:
        img_data_new, ext = await loop.run_in_executor(None, transform, img_data_old)
        del img_data_old
        if await check_base64_size(img_data_new, max_size):
            return img_data_new, ext
        img_data_old = img_data_new

    return None


def transform_rotate(img_data):
    logger.info('[TRANSFORM] Auto-rotate')
    img = load_image(img_data)
    img_rgb = tesseract.document_norm_rotate(image_rgb=np.array(img))
    img = load_image(img_rgb)

    byte_io = io.BytesIO()
    img.save(byte_io, 'JPEG')
    del img, img_data, img_rgb
    return byte_io.getvalue(), '.jpg'


def transform_gray(img_data):
    logger.info('[TRANSFORM] Convert to grayscale')
    img = load_image(img_data)
    img = ImageOps.grayscale(img)
    byte_io = io.BytesIO()

    img.save(byte_io, 'JPEG')
    del img, img_data
    return byte_io.getvalue(), '.jpg'


def transform_jpg(img_data):
    logger.info('[TRANSFORM] Convert to JPG')
    img = load_image(img_data)
    byte_io = io.BytesIO()

    img.save(byte_io, 'JPEG')
    del img, img_data
    return byte_io.getvalue(), '.jpg'


def transform_lower_res(img_data, factor=0.75):
    logger.info('[TRANSFORM] Scaling image 0.75x')
    img = load_image(img_data)
    img = ImageOps.scale(img, factor=factor)
    byte_io = io.BytesIO()

    img.save(byte_io, 'JPEG')
    del img, img_data
    return byte_io.getvalue(), '.jpg'


async def check_base64_size(data: [str, bytes], max_size=None):
    if not max_size:
        return True

    if isinstance(data, str):
        data = data.encode()

    file_data_bs = base64.b64encode(data).decode()
    res = len(file_data_bs) <= max_size
    del file_data_bs
    return res


def detect_extension(sig_4bytes: str) -> str | None:
    for ext, format_sigs in sigs.items():
        if sig_4bytes in format_sigs:
            return ext
    return None


def check_file_extension(file: UploadFile, only_images=False):
    name = file.filename
    file_data = file.file.read()
    type = file.content_type

    if not type:
        # Detect file type by signature
        sig = file_data[:4].hex()
        tiff = sig == tiff_sigs
        png = sig == png_sigs
        jpg = sig in [jpg_sigs]
        pdf = sig == pdf_sigs
        if not any([tiff, png, jpg, pdf]):
            if only_images:
                raise HTTPException(400, 'File is not an image or pdf type (not provided content-type)')
    elif all([
        not type.startswith('image'),
        not name.lower().endswith('.tiff'),
        not name.lower().endswith('.tif'),
        not name.lower().endswith('.pdf'),
    ]):
        if only_images:
            raise HTTPException(400, 'File is not an image or pdf type')

    file.file.seek(0)
    return file


async def convert_file_image(file: UploadFile, llm_type, auto_rotate=False, return_list=False) -> tuple[AsyncGenerator[tuple[bytes, str]], int]:
    gen = convert_file_image_count(file, llm_type, auto_rotate, return_list)
    count: int = await anext(gen)
    return gen, count


async def convert_file_image_count(file: UploadFile, llm_type, auto_rotate=False, return_list=False) -> AsyncGenerator[Union[tuple[bytes, str], int]]:
    name = file.filename
    file_data = file.file.read()
    del file
    max_size = max_sizes.get(llm_type)
    reduce_transforms = [transform_jpg, transform_lower_res, transform_lower_res, transform_gray]
    transforms = []
    if auto_rotate:
        transforms = [transform_rotate]

    detected_ext = detect_extension(file_data[:4].hex())
    base_name, ext = os.path.splitext(name)

    if detected_ext != ext and detected_ext is not None:
        logger.info(f'Detected extension {detected_ext} on file {name}. Renaming')
        name = f'{base_name}{detected_ext}'
        ext = detected_ext

    if ext in ['.tiff', '.tif'] or (len(file_data) > 4 and file_data[:4].hex() in tiff_sigs):
        new_name = f'{os.path.splitext(name)[0]}.png'
        logger.info(f"Convert {name} to {new_name}")

        img = Image.open(io.BytesIO(file_data))
        byte_io = io.BytesIO()
        img.save(byte_io, 'PNG')
        file_data = byte_io.getvalue()
        del img, byte_io
        name = new_name

    if ext == '.pdf' or (len(file_data) > 4 and file_data[:4].hex() in pdf_sigs):
        base_name = f'{os.path.splitext(name)[0]}'

        page_num = file_formats.get_pdf_page_count(stream=io.BytesIO(file_data))

        yield page_num
        for page in range(1, page_num + 1):
            logger.info(f"[CONVERT] {name} {page}/{page_num} to JPEG")
            img = pdf2image.convert_from_bytes(file_data, dpi=300, first_page=page, last_page=page)[0]
            byte_io = io.BytesIO()
            img.save(byte_io, 'JPEG')
            del img

            result_data = byte_io.getvalue()
            name = f'{base_name}_{page}.jpg'
            logger.info(f'[TRANSFORM] {name} Processing page {page} / {page_num}')
            result_data, ext = await transform_image(result_data, max_size, transforms, reduce_transforms)
            if ext:
                name = f'{os.path.splitext(name)[0]}{ext}'

            yield result_data, name

        return

    result_data, ext = await transform_image(file_data, max_size, transforms, reduce_transforms)
    if ext:
        name = f'{os.path.splitext(name)[0]}{ext}'

    yield 1
    yield result_data, name


async def process_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session'
):
    try:
        output_diff = await _process_detection(org, ws, llm, detection, det, out, llm_type, access_type=access_type)
    except Exception as e:
        msg = f'{e.__class__.__name__}: {str(e)}'
        updated_det = {
            'output': msg,
            'status': models.STATUS_ERROR,
        }

        await db_api.update_detection_item(det, {'status': updated_det['status']})
        await db_api.update_detection_item_output(out, updated_det)
        logger.exception(msg)
    else:
        updated_det = {'status': models.STATUS_SUCCESS}
        updated_det.update(output_diff)
        if 'output' in updated_det:
            updated_det['output'] = updated_det['output'].replace('\x00', '')

        await db_api.update_detection_item(det, {'status': updated_det['status']})
        await db_api.update_detection_item_output(out, updated_det)

    await db_api.update_metrics_for_detection(det)


def build_message(file_data):
    if isinstance(file_data, str):
        return {"type": "text", "text": file_data}

    file_data_bs = base64.b64encode(file_data)
    ext = detect_extension(file_data[:4].hex())

    mimetype = mimetypes.guess_type(f'a{ext}')[0]
    return {
        "type": "image_url", "image_url": {"url": f"data:{mimetype};base64,{file_data_bs.decode()}"}
    }


async def extract_document_data(
    filename, file_data, llm_type, llm, prompt,
    mode: Literal['llm', 'ocr', 'text', 'force_llm', 'mixed_llm'] = 'llm'
):
    # Let the main thread go
    await asyncio.sleep(SMALL_SLEEP)

    loop = asyncio.get_event_loop()
    ext = detect_extension(file_data[:4].hex())
    logger.info(f'Detected extension {ext} on file {filename}')
    if not ext:
        _, ext = os.path.splitext(filename)

    # Additional check for pdf if contain images
    if ext.lower() == '.pdf':
        if not file_formats.pdf_contains_images(stream=io.BytesIO(file_data)):
            mode = 'text'

    if ext.lower() in ['.pdf'] and mode == 'mixed_llm':
        # pages with images -> go check and convert
        # pages without images -> parse text mode
        # 1. Get page map where images are

        file_struct = UploadFile(io.BytesIO(file_data), size=len(file_data), filename=filename)
        file_datas, file_count = await convert_file_image(file_struct, llm_type, auto_rotate=True, return_list=True)

        docs = []
        i = 0
        async for data, name in file_datas:
            message_content = [
                {"type": "text", "text": prompt},
                build_message(data)
            ]

            messages = [HumanMessage(content=message_content)]
            output = await invoke_with_retry(llm, messages)
            logger.info(f'[{i + 1}/{file_count}] Recognized doc: {output.content}')
            sub_docs = file_formats.parse_as_text(output.content, filename, page=i + 1, total_pages=file_count)
            docs.extend(sub_docs)
            del data
            del message_content
            i += 1
    elif ext.lower() in to_recognition and mode in ['llm', 'force_llm', 'mixed_llm']:
        file_struct = UploadFile(io.BytesIO(file_data), size=len(file_data), filename=filename)
        file_datas, file_count = await convert_file_image(file_struct, llm_type, auto_rotate=True, return_list=True)

        docs = []
        i = 0
        async for data, name in file_datas:
            message_content = [
                {"type": "text", "text": prompt},
                build_message(data)
            ]

            messages = [HumanMessage(content=message_content)]
            output = await invoke_with_retry(llm, messages)
            logger.info(f'[{i+1}/{file_count}] Recognized doc: {output.content}')
            sub_docs = file_formats.parse_as_text(output.content, filename, page=i+1, total_pages=file_count)
            docs.extend(sub_docs)
            del data
            del message_content
            i += 1

    else:
        load_file = functools.partial(file_formats.load_file, additional_meta={})
        docs = await loop.run_in_executor(None, load_file, file_data, filename)
        if mode == 'force_llm':
            # Extract using prompt
            message_content = [
                {"type": "text", "text": prompt},
                build_message('\n\n'.join([d.page_content for d in docs]))
            ]
            messages = [HumanMessage(content=message_content)]
            output = await llm.ainvoke(messages)
            docs = file_formats.parse_as_text(output.content, filename, page=1, total_pages=1)

        del file_data

    return docs


@retry_utils.retry(IndexError, tries=3, delay=1, jitter=2)
async def invoke_with_retry(llm, messages):
    return await llm.ainvoke(messages)


async def process_metadata(
    extractor: models.Extractor, ext_file: models.DatasetFile, file_data, llm_type, llm: BaseChatModel, prompt,
    extracted_docs: list[Document], access_type='session'
):
    try:
        metadata = await extract_document_metadata(extracted_docs, llm, prompt)
        meta = extract_json(metadata)
    except Exception as exc:
        logger.exception(str(exc))
        status = models.STATUS_ERROR
        msg = f'{exc.__class__.__name__}: {str(exc)}'
        meta = {}
    else:
        msg = None
        status = models.STATUS_SUCCESS

    json_status = {'status': status, 'message': msg}
    await db_api.update_dataset_file(
        ext_file,
        {'status': json_status, 'meta': meta}
    )
    logger.info(f"Metadata extracted: {meta}")
    await db_api.update_extractor(extractor, {'status': json_status})
    return json_status


async def extract_document_metadata(extracted_docs: list[Document], llm: BaseChatModel, prompt):
    file_data = '\n\n'.join([d.page_content for d in extracted_docs])

    # Truncate file_data to the according token limit of the model
    model_name = llm_utils.get_model_name(llm) or "gpt-4o"
    context_limit = get_model_context_limit(model_name)
    # Count tokens in the document and prompt
    doc_tokens = count_tokens(file_data, model_name)
    prompt_tokens = count_tokens(prompt, model_name)
    # Drop max tokens
    if hasattr(llm, 'max_tokens'):
        llm.max_tokens = None
    if doc_tokens + prompt_tokens + 2000 > context_limit:
        file_data = split_document(Document(
            page_content=file_data), context_limit - prompt_tokens - 2000, model_name
        )[0].page_content

    messages = [
        SystemMessage(content=prompt),
        HumanMessage(content=[build_message(file_data)])
    ]
    output = await llm.ainvoke(messages)
    return output.content


async def _process_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session', extraction_mode: str = 'text'
) -> dict:
    if det.type in ['prescription', 'prompt']:
        return await _process_prompt_detection(org, ws, llm, detection, det, out, llm_type, access_type, extraction_mode)
    elif det.type == 'posology':
        return await _process_posology_detection(org, ws, llm, detection, det, out, llm_type, access_type, extraction_mode)
    elif det.type == 'barcode':
        return await _process_barcode_detection(org, ws, llm, detection, det, out, llm_type, access_type, extraction_mode)
    else:
        raise ValueError(f"Unrecognized detection type '{det.type}'")


async def _process_prompt_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session', extraction_mode: str = 'text'
) -> dict:
    config = detection.get_config()
    prompt = config['prompts']['system_prompt']

    messages = [{"type": "text", "text": prompt}]
    files = await db_api.list_detection_files(detection_item_id=det.id)
    files = files or []
    if len(files) < 1:
        raise ValueError('Uploaded file not found')
    file = files[0]

    if not file.source_url:
        path = get_detection_file_path(org.name, ws.name, file)
        result = await SharedConfig().file_manager.read_file(path)
        file_data = await SharedConfig().file_manager.get_data(result)
    else:
        file_data = await download_content(file.source_url)

    _, ext = os.path.splitext(file.name)

    # if image -> go check and convert, assume one page
    if ext in image_extensions:
        file_struct = UploadFile(io.BytesIO(file_data), size=len(file_data), filename=file.name)
        file_datas_gen, file_count = await convert_file_image(
            file_struct, llm_type=llm_type, auto_rotate=True, return_list=True
        )
    else:
        # if pdf, check extraction_mode -> go either check_and_convert, or parse text mode
        if extraction_mode in ['text', 'ocr']:
            _, docs = file_formats.load_and_split_file(file_data, file.name, additional_meta={'file_id': file.id})
            full_text = '\n\n'.join([d.page_content for d in docs])

            file_datas_gen = gen_item((full_text, None))
        else:
            file_struct = UploadFile(io.BytesIO(file_data), size=len(file_data), filename=file.name)
            file_datas_gen, file_count = await convert_file_image(
                file_struct, llm_type=llm_type, auto_rotate=True, return_list=False
            )

    async for data, _ in file_datas_gen:
        messages.append(build_message(data))
    delete_file_api = access_type == 'token--workspace' and not config.get('keep_files_api', False)
    delete_file_ui = access_type != 'token--workspace' and not config.get('keep_files', True)
    if delete_file_ui or delete_file_api:
        # TODO temporary fix
        if not file.source_url:
            await SharedConfig().file_manager.delete_file(path)
        await db_api.update_detection_file(file.id, {'status': models.STATUS_DELETED})

    output = await llm.ainvoke([
        HumanMessage(content=messages),
    ])
    del messages
    logger.info(f"[DETECTION] Output: {output}")

    return {'output': output.content}


async def gen_item(item):
    yield item


# noinspection PyInconsistentReturns
async def _process_posology_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session', extraction_mode: str = 'text'
) -> dict[str, Any]:
    config = detection.get_config()
    start_text = config.get('posology', {}).get('start_text', r'\n[|#*_\s]*4.2[*_.\s]+Poso')
    end_text = config.get('posology', {}).get('end_text', r'\n[|#*_\s]*4.3[*_.\s]+Contr')
    from_line_start = config.get('posology', {}).get('from_line_start', True)
    section_pattern = config.get('posology', {}).get('section_pattern', r'rubriques?\s+[0-9.,\set]+[0-9.,]')
    image_dpi = int(config.get('posology', {}).get('image_dpi', 100))
    style_map = config.get('posology', {}).get('styles_map', {
        '.AmmAnnexeTitre1': 'h2',
        '.AmmAnnexeTitre2': 'h3',
        '.AmmAnnexeTitre3': 'h4',
        '.AmmAnnexeTitre4': 'h5',
        '.AmmCorpsTexteGras': 'b',
    })

    files = await db_api.list_detection_files(detection_item_id=det.id)
    files = files or []
    if len(files) < 1:
        raise ValueError('Uploaded file not found')

    file = files[0]

    base, ext = os.path.splitext(file.name)
    if ext not in ['.pdf', '.html', '.htm']:
        raise ValueError('Currently posology detector works only with PDF and HTML files.')

    if not file.source_url:
        path = get_detection_file_path(org.name, ws.name, file)
        result = await SharedConfig().file_manager.read_file(path)
        file_data = await SharedConfig().file_manager.get_data(result)
    else:
        file_data = await download_content(file.source_url)

    more_kwargs = {}
    if ext.lower() == '.pdf':
        extract_func = posology.extract_section_pymupdf4llm
    else:
        extract_func = posology.extract_section_html
        more_kwargs = {'url': file.source_url}

    dirname, saved_path = posology.save_to_tmp_dir(data=file_data, name=file.name)

    zip_file = io.BytesIO()
    write_metrics_handle = write_metrics(
        ws.id,
        org.id,
        models.APP_TYPE_DETECTION,
        det.detection_id,
        out.id,
        model_name=None,
    )
    try:
        # output = await loop.run_in_executor(None, extract_section_pymupdf4llm, saved_path, zip_file)
        wrapped_func = write_metrics_handle(extract_func)
        output = await wrapped_func(
            saved_path,
            zip_file,
            start_text=start_text,
            end_text=end_text,
            from_line_start=from_line_start,
            section_pattern=section_pattern,
            image_dpi=image_dpi,
            style_map=style_map,
            **more_kwargs
        )
    finally:
        # Cleanup
        shutil.rmtree(dirname)

    delete_file_api = access_type == 'token--workspace' and not config.get('keep_files_api', False)
    delete_file_ui = access_type != 'token--workspace' and not config.get('keep_files', True)
    if delete_file_ui or delete_file_api:
        # TODO temporary fix
        if not file.source_url:
            await SharedConfig().file_manager.delete_file(path)
        await db_api.update_detection_file(file.id, {'status': models.STATUS_DELETED})

    logger.info(f"[POSOLOGY] Output: {output}")

    zip_data = zip_file.getvalue()
    output_file = await db_api.create_detection_file({
        'detection_item_id': 0,
        'name': f'{base}.zip',
        'size': len(zip_data),
        'status': models.STATUS_SUCCESS,
        'owner_id': det.owner_id,
        'workspace_id': det.workspace_id,
    })
    output_file_path = get_detection_file_path(org_name=org.name, workspace_name=ws.name, file=output_file)
    await SharedConfig().file_manager.save_file(output_file_path, zip_data)

    return {'output_file_id': output_file.id, 'output': output}


# noinspection PyInconsistentReturns
async def _process_barcode_detection(
    org, ws, llm: BaseChatModel,
    detection: models.Detection, det: models.DetectionItem, out: models.DetectionItemOutput,
    llm_type: str, access_type='session', extraction_mode: str = 'text'
) -> dict[str, Any]:
    config = detection.get_config()
    image_dpi = int(config.get('barcode', {}).get('image_dpi', 324))
    all_images = bool(config.get('barcode', {}).get('extract_all_images', True))

    files = await db_api.list_detection_files(detection_item_id=det.id)
    files = files or []
    if len(files) < 1:
        raise ValueError('Uploaded file not found')

    file = files[0]
    if not file.source_url:
        path = get_detection_file_path(org.name, ws.name, file)
        result = await SharedConfig().file_manager.read_file(path)
        file_data = await SharedConfig().file_manager.get_data(result)
    else:
        file_data = await download_content(file.source_url)

    base, ext = os.path.splitext(file.name)
    if ext != '.pdf':
        raise ValueError('Currently posology detector works only with PDF files.')

    dirname, saved_path = posology.save_to_tmp_dir(data=file_data, name=file.name)

    zip_file = io.BytesIO()
    write_metrics_handle = write_metrics(
        ws.id,
        org.id,
        models.APP_TYPE_DETECTION,
        det.detection_id,
        out.id,
        model_name=None,
    )
    try:
        # output = await loop.run_in_executor(None, extract_section_pymupdf4llm, saved_path, zip_file)
        wrapped_func = write_metrics_handle(barcode.extract_barcodes)
        output = await wrapped_func(
            saved_path,
            zip_file,
            image_dpi=image_dpi,
            impl='opencv',
            extract_all_images=all_images,
        )
    finally:
        # Cleanup
        shutil.rmtree(dirname)

    delete_file_api = access_type == 'token--workspace' and not config.get('keep_files_api', False)
    delete_file_ui = access_type != 'token--workspace' and not config.get('keep_files', True)
    if delete_file_ui or delete_file_api:
        # TODO temporary fix
        if not file.source_url:
            await SharedConfig().file_manager.delete_file(path)
        await db_api.update_detection_file(file.id, {'status': models.STATUS_DELETED})

    logger.info(f"[BARCODE] Detected barcodes in {file.name}: {output}")

    zip_data = zip_file.getvalue()
    output_file = await db_api.create_detection_file({
        'detection_item_id': 0,
        'name': f'{base}.zip',
        'size': len(zip_data),
        'status': models.STATUS_SUCCESS,
        'owner_id': det.owner_id,
        'workspace_id': det.workspace_id,
    })
    output_file_path = get_detection_file_path(org_name=org.name, workspace_name=ws.name, file=output_file)
    await SharedConfig().file_manager.save_file(output_file_path, zip_data)

    return {'output_file_id': output_file.id, 'output': output}


def get_detection_file_path(org_name: str, workspace_name: str, file: models.DetectionItemFile):
    name, ext = os.path.splitext(file.name)
    return f'detection_files/{org_name}/{workspace_name}/{file.workspace_id}/{file.name}/{file.id}{ext}'


def get_extractor_file_path(org_name: str, workspace_name: str, file: models.ExtractorFile):
    name, ext = os.path.splitext(file.name)
    return f'extractor_files/{org_name}/{workspace_name}/{file.workspace_id}/{file.name}/{file.id}{ext}'
