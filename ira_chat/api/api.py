import asyncio
import contextlib
import json
import logging
import os
import time
from typing import Optional
from urllib import parse as urlparse

import nest_asyncio
import starlette.responses
from fastapi import APIRouter, HTTPException
from fastapi import FastAPI
from fastapi import Request
from fastapi import Response
from fastapi.responses import J<PERSON><PERSON>esponse

from ira_chat import oauth
from ira_chat.api import auth
from ira_chat.api import auth_utils, datasets
from ira_chat.api import chats
from ira_chat.api import common_utils
from ira_chat.api import confirm
from ira_chat.api import connect
from ira_chat.api import detection
from ira_chat.api import handle_error
from ira_chat.api import metrics
from ira_chat.api import org
from ira_chat.api import tokens
from ira_chat.api import workspaces
from ira_chat.cache import global_cache as cache
from ira_chat.context import context as ctx
from ira_chat.credentials import credentials
from ira_chat.db import api as db_api, base
from ira_chat.db import models
from ira_chat.e_mail import send
from ira_chat.policies import workspace as ws_policy, policies
from ira_chat.services import jobs
from ira_chat.utils import utils, alembic_utils

OK_RESULT = '{"message": "OK"}\n'

OPENAPI_PREFIX = '/openapi.'
API_WS_PREFIX = '/api/v1/workspaces/'
logger = logging.getLogger(__name__)
without_auth = {
    '/api/v1/auth/login',
    '/api/v1/auth/info',
    '/api/v1/auth/register',
    '/api/v1/auth/reset_password',
    '/api/v1/confirm',
    '/api/v1/workspaces',
    '/api/v1/organization/info',
}
skip_session = {
    '/api/v1/new_session_id',
    '/api/v1/example',
    '/api/v1/connections/available',
}
skip_auth = {
    '/api/v1/message',
}
skip_auth_prefix = {
    '/api/v1/connections/login/',
    '/api/v1/connections/callback/',
}


def get_router():
    root = APIRouter()
    api_router = APIRouter(prefix=utils.API_PREFIX)

    api_router.include_router(auth.get_router(), tags=["Auth"])
    api_router.include_router(chats.get_router(), tags=["Chats"])
    api_router.include_router(connect.get_router(), tags=["Connections"])
    api_router.include_router(datasets.get_router(), tags=["Datasets"])
    # Detection apps
    detections = detection.get_routers(prefix='/')
    for r in detections:
        api_router.include_router(r, tags=['Detections'])
    # api_router.include_router(files.get_router(), tags=["Files"])
    api_router.include_router(tokens.get_router(), tags=["Tokens"])
    api_router.include_router(metrics.get_router(), tags=["Metrics (org)"])
    for r in workspaces.get_routers():
        api_router.include_router(r, tags=["Workspaces"])
    for r in org.get_routers():
        api_router.include_router(r, tags=["Organizations"])
    api_router.include_router(confirm.get_router(), tags=["Confirm"])

    api_router.add_api_route("/new_session_id", new_session_id, methods=['GET'], tags=['Session'])
    api_router.add_api_route("/example", example_api_endpoint, methods=['GET'], tags=['Example'])
    api_router.add_api_route("/example", example_api_endpoint_post, methods=['POST'], tags=['Example'])
    api_router.add_api_route("/message", message_show, methods=['GET'], tags=['Example'])

    root.include_router(api_router)
    root.add_route("/health", health, methods=['GET'], name="health")

    return root


async def time_middleware(request: Request, call_next):
    if request.url.path == '/health':
        return await call_next(request)

    t = time.time()
    response = await call_next(request)
    elapsed_ms = f'{(time.time() - t) * 1000:9.3f}ms'
    urlpath = request.url.path + (('?' + request.url.query) if request.url.query else '')
    message = f'{response.status_code} | {elapsed_ms} | {request.method:7} | {urlpath}'

    logger.info(message)

    return response


async def last_seen_process(request: Request):
    if request.method not in ['POST', 'PUT', 'DELETE']:
        return

    u = ctx.current_user()
    if not u:
        return
    _ = asyncio.create_task(
        utils.log_exceptions(db_api.update_user(u, {'last_activity_at': models.now()}))
    )


async def req_middleware(request: Request, call_next):
    request_dummy = ctx.request.set(request)

    if not request.url.path.startswith((utils.API_PREFIX, OPENAPI_PREFIX)):
        return await call_next(request)

    if request.url.path in skip_session:
        return await call_next(request)

    cache_result = None
    domain = common_utils.get_org_host(request)
    auth_token, need_session_handle = await token_auth_if_any(request, domain)
    if need_session_handle:
        try:
            session_created, cache_result = await auth_utils.session_handler_cache(request, domain)
        except HTTPException as e:
            return handle_error.handle_exception(request, e)
    else:
        session_created = False

    if request.url.path in skip_auth:
        return await call_next(request)
    for skip_prefix in skip_auth_prefix:
        if request.url.path.startswith(skip_prefix):
            return await call_next(request)

    cache_key = common_utils.get_cache_key(ctx.current_session().id, request, domain=domain)

    # Set organization state properties
    if cache_result:
        _, org, user, org_user, current_role = cache_result
        ctx.org.set(org)
    else:
        org, user, org_user, current_role = None, None, None, None

    async with base.session_context():
        if not ctx.current_org():
            org = await db_api.get_org_by_domain(domain)
            if org is None:
                return handle_error.error_response(400, f'Organization not found for: {domain}')
            ctx.org.set(org)
        else:
            org = ctx.current_org()

        # Add workspace to context
        ws = None
        ws_cache_key = None
        ws_cache_result = None
        if request.url.path.startswith(API_WS_PREFIX):
            ws_id = request.url.path[len(API_WS_PREFIX):].split('/')[0]
            if not ws_id.isdigit():
                return handle_error.error_response(400, f'Invalid workspace id: {ws_id}')
            ws_cache_key = common_utils.get_ws_cache_key(ctx.current_session(), ws_id)
            ws_cache_result = await cache.get_cache().aget(ws_cache_key)

            if ctx.current_workspace() is not None and ctx.current_workspace().id != int(ws_id):
                return handle_error.error_response(403, f'Access is granted to another workspace: {ctx.current_workspace().id}')

            elif not ctx.current_workspace():
                if not ws_cache_result:
                    ws = await db_api.get_workspace_by_id(int(ws_id), notfoundok=True)
                else:
                    ws, *_ = ws_cache_result

                if ws is None:
                    return handle_error.error_response(404, f'Workspace not found for id: {ws_id}')
                ctx.workspace.set(ws)

        # if request.url.path in without_auth:
        #     return await call_next(request)

        if not hasattr(ctx.request.value.state, 'permissions'):
            # Default permissions
            permissions = 0
        else:
            permissions = ctx.request.value.state.permissions

        ws_allowed = False
        group_ids = []
        if ws:
            if ws_cache_result:
                _, ws_allowed, permissions, group_ids = ws_cache_result
            else:
                ws_allowed, permissions, group_ids = await ws_policy.check_user_in_workspace(
                    ctx.session.value.user_id, ws
                )
                await cache.get_cache().aset(ws_cache_key, (ws, ws_allowed, permissions, group_ids))

        if not auth_utils.ephemeral_user(ctx.session.value):
            if not user:
                if ctx.current_session().user_id == -1:
                    user = models.User(id=-1, name='admin', login=f'admin@{domain}', confirmed=True)
                else:
                    user = await db_api.get_user_by_id(ctx.session.value.user_id)

            if user.id in credentials.ADMIN_IDS or ctx.current_session().admin:
                ctx.current_session().admin = True
                permissions = policies.AccessMode.OWNER
                ws_allowed = True

            if not org_user:
                if ctx.current_session().user_id == -1:
                    current_role = {'permissions': policies.OrgAccessMode.OWNER}
                else:
                    org_user = await db_api.get_org_user(org.id, user.id)
                    if org_user:
                        current_role = db_api.get_role_by_id(org_user.role_id)
                    else:
                        current_role = {'permissions': policies.OrgAccessMode.USER}
            
            org_user_permissions = current_role['permissions']

            if org_user:
                permissions, allowed = auth_utils.extend_ws_permissions_with_org(permissions, org_user_permissions)
                if allowed:
                    ws_allowed = True

    _set_session_permissions(
        ctx.current_session(), org, user, org_user, current_role, ws, ws_allowed, permissions, group_ids
    )
    if not cache_result:
        await cache.get_cache().aset(cache_key, (ctx.current_session(), org, user, org_user, current_role))

    if not hasattr(ctx.request.value.state, 'access_type'):
        ctx.request.value.state.access_type = 'session'

    if ws and not ctx.request.value.state.ws_allowed:
        return handle_error.error_response(403, 'Workspace not allowed')

    # async with async_session() as db_session:
    #     async with db_session.begin():
    #         db_sess = db.set(db_session)
    #         response = await call_next(request)
    #         db.reset(db_sess)

    response: starlette.responses.Response = await call_next(request)
    if session_created:
        response.set_cookie(
            key=auth_utils.COOKIE_KEY,
            value=ctx.session.value.id,
            expires=ctx.session.value.ttl,
            httponly=True,
            domain=get_cookie_domain(request)
        )

    await last_seen_process(request)

    ctx.request.reset(request_dummy)
    return response


def _set_session_permissions(session, org, user, org_user, current_role, ws, ws_allowed, permissions, group_ids):
    ctx.org.set(org)
    ctx.request.value.state.permissions = permissions
    ctx.request.value.state.ws_allowed = ws_allowed
    ctx.request.value.state.group_ids = group_ids

    if ws:
        ctx.workspace.set(ws)

    if user and user.id in credentials.ADMIN_IDS:
        ctx.current_session().admin = True

    if not auth_utils.ephemeral_user(ctx.session.value):
        ctx.request.value.state.user = user
        ctx.request.value.state.org_user = org_user
        ctx.request.value.state.org_user_role = current_role


def _set_token_permissions(token_db, session, org, ws):
    ctx.session.set(session)

    ctx.org.set(org)
    if ws:
        ctx.workspace.set(ws)
    ctx.request.value.state.permissions = token_db.permissions
    if token_db.org_permissions is not None:
        ctx.request.value.state.org_permissions = token_db.org_permissions
        ctx.request.value.state.org_user = models.OrganizationUser(user_id=session.user_id, org_id=org.id)

    if token_db.object_type == models.TOKEN_USER:
        ctx.request.value.state.org_user = models.OrganizationUser(user_id=session.user_id, org_id=org.id)
    elif token_db.object_type == models.TOKEN_WORKSPACE:
        ctx.request.value.state.ws_allowed = True
    ctx.request.value.state.access_type = f'token--{token_db.object_type}'

    ctx.request.value.state.group_ids = []


async def token_auth_if_any(request: Request, domain: str):
    token, ephemeral_ses_id = auth_utils.get_bearer_token(request)
    if not token:
        return token, True
    if not ephemeral_ses_id:
        ephemeral_ses_id = token

    cache_key = f'token/{token}/{ephemeral_ses_id}'
    cache_result = await cache.get_cache().aget(cache_key)
    if cache_result:
        token_db, session, org, ws = cache_result
        _set_token_permissions(token_db, session, org, ws)
        return token, token_db.object_type == models.TOKEN_USER

    token_db: models.Token = await db_api.get_token_by_id(token)
    if not token_db:
        raise HTTPException(403, 'Token is invalid')

    token_type = token_db.object_type
    session_handle = token_db.object_type == models.TOKEN_USER

    session, _ = await auth_utils.get_or_create_session_for_id(ephemeral_ses_id)
    if token_type == models.TOKEN_WORKSPACE:
        # Init ws-related stuff
        ws = await db_api.get_workspace_by_id(token_db.object_id, notfoundok=True)
        if not ws:
            raise HTTPException(403, 'Token workspace is invalid')

        org = await db_api.get_org_by_id(ws.org_id)
        _set_token_permissions(token_db, session, org, ws)
        await cache.get_cache().aset(cache_key, (token_db, session, org, ws))
    elif token_type == models.TOKEN_USER:
        # Init user-related stuff
        if session.user_id != token_db.owner_id:
            session = await db_api.update_session(session, {'user_id': token_db.owner_id})
        org = await db_api.get_org_by_domain(domain)
        _set_token_permissions(token_db, session, org, None)
        await cache.get_cache().aset(cache_key, (token_db, session, org, None))

    return token, session_handle


def get_cookie_domain(req: Request):
    org_host = common_utils.get_org_host(req)
    base_url = os.environ['BASE_URL']
    base_host = urlparse.urlparse(base_url).hostname
    if base_host in org_host:
        return base_host
    return None


@contextlib.asynccontextmanager
async def lifespan(app: FastAPI):
    await startup()
    yield
    await shutdown()


async def startup():
    asyncio_loop = os.getenv('ASYNCIO_LOOP', 'uvloop')
    if asyncio_loop == 'asyncio':
        nest_asyncio.apply()

    utils.setup_logging()
    send.init_smtp()
    oauth.init()
    os.environ['RAGAS_DO_NOT_TRACK'] = "true"

    alembic_utils.run_migrations('migrations')

    logger.info(f'Using asyncio loop: {asyncio_loop}')
    logger.info("Starting the server...")
    # async with base.get_engine().begin() as conn:
    #     await conn.run_sync(models.Session.metadata.create_all)


async def shutdown():
    if jobs.JOB_COUNTER.total() != 0:
        logger.info(f"Wait for {jobs.JOB_COUNTER.total()} jobs to complete...")
        while jobs.JOB_COUNTER.total() > 0:
            await asyncio.sleep(1)
    logger.info("Shutdown the server...")


async def health(request):
    return Response(content=OK_RESULT, status_code=200)


async def message_show(text: Optional[str] = None, error: Optional[str] = None):
    msg = {'message': 'OK'}
    if text:
        msg = {'message': text}
    if error:
        msg = {'error': error}
    return JSONResponse(content=msg, status_code=200)


async def example_api_endpoint(data: Request):
    return JSONResponse(content={
        "query": {k: v for k, v in data.query_params.items()},
        "headers": {k: v for k, v in data.headers.items()},
    }, status_code=200)


async def new_session_id():
    return JSONResponse(content={"id": utils.generate_unicode_uuid()}, status_code=200)


async def example_api_endpoint_post(data: Request):
    data_body = await data.body()
    logger.info(f'[Example] Data: {data_body.decode()}')
    logger.info(f'[Example] Headers: { {k: v for k, v in data.headers.items()} }')
    return JSONResponse(content={
        "message": json.loads(data_body),
        "headers": {k: v for k, v in data.headers.items()},
        "query": {k: v for k, v in data.query_params.items()},
    })
