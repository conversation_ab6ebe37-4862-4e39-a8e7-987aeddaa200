import mimetypes

import httpx


async def estimate_url_extension(url: str) -> str:
    client = httpx.AsyncClient()
    resp = await client.head(url)
    resp.raise_for_status()
    mimetype = resp.headers.get('content-type', 'text/html').split(';')[0]
    await client.aclose()
    await resp.aclose()
    extension = mimetypes.guess_extension(mimetype)

    return extension


async def download_content(url: str) -> bytes:
    client = httpx.AsyncClient()
    resp = await client.get(url)
    resp.raise_for_status()
    await client.aclose()

    return await resp.aread()


def download_content_sync(url: str, get_content_type: bool = False):
    client = httpx.Client()
    resp = client.get(url)
    resp.raise_for_status()
    client.close()

    if not get_content_type:
        return resp.read()
    else:
        return resp.read(), resp.headers.get('Content-Type', '').split(';')[0]
