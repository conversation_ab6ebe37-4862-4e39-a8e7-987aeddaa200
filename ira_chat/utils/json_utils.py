import copy
import datetime
import io
import json
import logging
import re
from typing import MutableMapping, Literal

import jsf
import jsondiff
import jsonschema
import pandas as pd
from fastapi import HTTPException
from langchain_core.language_models import BaseChatModel

from ira_chat.utils import llm_utils
from ira_chat.db import base

logger = logging.getLogger(__name__)

EMPTY_VALUES = {"", None, 0}
NA_VALUES = {"N/A", "Not provided", "", None, 0}
MERGE_LISTS_PROMPT = """Merge two JSON lists carefully to produce a new JSON list while keeping the important information.
If some values seem correct but in conflict, then give a preference to values from "two".
If some values completely diverge - then a new element should be created.
one:

{one}

two:

{two}

Merged output:
"""


def flatten_json(json_obj, parent_key='', sep='::', level=0):
    items = []
    level += 1
    indent = ' ' * ((level - 1) // 2)
    # indent = ''
    for k, v in json_obj.items():
        # new_key = f"{parent_key}{sep}{k}" if parent_key else k
        new_key = f"{indent}{k}" if parent_key else k
        if isinstance(v, MutableMapping):
            items.append((new_key, ''))
            items.extend(flatten_json(v, new_key, sep=sep, level=level + 1))
        elif isinstance(v, list):
            for i, sub_item in enumerate(v):
                if isinstance(sub_item, dict):
                    new_sub_key = f"{new_key}{sep}{i}"
                    items.extend(flatten_json(sub_item, new_sub_key, sep=sep, level=level + 1))
                else:
                    items.append((new_key, sub_item))
        else:
            items.append((new_key, v))
    return items


def merge_jsons(jsons: list[dict], llm: BaseChatModel = None) -> dict:
    if not jsons:
        return {}
    if len(jsons) == 1:
        return jsons[0]

    result = jsons[0]
    mode = 'overwrite_append' if not llm else 'overwrite_llm'
    if llm is not None:
        llm = llm_utils.set_cheapest_model(llm)
    for obj in jsons[1:]:
        result = dict_merge(result, obj, mode=mode, llm=llm, verbose=True)

    return result


def generate_dummy_json(schema: dict, mode: Literal['null', 'default'] = 'default') -> dict:
    example = jsf.JSF(schema).generate()
    if not example:
        # Probably not schema
        dummy = _fill_json(schema, mode=mode)
    else:
        dummy = _fill_json(example, mode=mode)

    return _keep_1_list(dummy)


def compare_dict_structure(one: dict, two: dict) -> bool:
    if set(list(one.keys())) != set(list(two.keys())):
        return False

    all_compare = True
    for k, v in one.items():
        if isinstance(v, dict):
            all_compare = all_compare and compare_dict_structure(one[k], two[k])
        if isinstance(v, list):
            all_compare = all_compare and compare_list_structure(one[k], two[k])

    return all_compare


def validate_schema(data: dict, schema: dict):
    try:
        jsonschema.validate(data, schema)
    except Exception as e:
        raise HTTPException(400, str(e))


def compare_list_structure(one: list, two: list):
    if not one or not two:
        return True

    f_one = one[0]
    f_two = two[0]
    if isinstance(f_one, dict) and isinstance(f_two, dict):
        return compare_dict_structure(f_one, f_two)
    return True


def diff_json(source, target):
    return jsondiff.diff(source, target, marshal=True)


def compare_db_json(source: dict, target: dict) -> bool:
    if not source and not target:
        return True

    if not source or not target:
        return False

    none_values = [k for k, v in source.items() if v is None]
    for k in none_values:
        source.pop(k, None)

    exclude_fields = ['id', 'client_id', 'created_at', 'updated_at']
    for k in exclude_fields:
        source.pop(k, None)
        target.pop(k, None)

    # Handle datetime / date
    for k, v in source.items():
        if isinstance(v, datetime.datetime):
            source[k] = base.date_to_string(v)
        elif isinstance(v, datetime.date):
            source[k] = base.dt_to_string(v)

    for k, v in target.items():
        if isinstance(v, datetime.datetime):
            target[k] = base.date_to_string(v)
        elif isinstance(v, datetime.date):
            target[k] = base.dt_to_string(v)

    return diff_json(source, target) == {}


def patch_json(source, patch):
    return jsondiff.patch(source, patch, marshal=True)


def set_history_item(one: dict, two: dict, diff, history_record, history) -> dict:
    one_keys = set(one.keys())
    two_keys = set(two.keys())
    common_keys = one_keys.intersection(two_keys)
    only_one = one_keys.difference(two_keys)
    only_two = two_keys.difference(one_keys)

    for k in common_keys:
        if isinstance(one[k], dict):
            if k in diff:
                history[k] = set_history_item(one[k], two[k], diff[k], history_record, history[k])
        # if isinstance(one[k], list):
        # Add new item

        # pass
        else:
            if k in diff:
                val_item = copy.copy(history_record)
                val_item['value'] = two[k]
                val_item['diff'] = copy.copy(diff[k])

                if isinstance(history[k], list) and len(history[k]) > 0 and 'point_id' in history[k][0]:
                    # Append history item
                    history[k].append(val_item)
                else:
                    # Create history item list
                    history[k] = [val_item]

    # Process keys present only in dict two (newly added keys).
    for k in only_two:
        # When the value is a dict, record the entire subtree.
        if isinstance(two[k], dict):
            val_item = copy.copy(history_record)
            val_item['value'] = two[k]
            val_item['diff'] = diff[k]
            history[k] = [val_item]
        else:
            val_item = copy.copy(history_record)
            val_item['value'] = two[k]
            val_item['diff'] = diff[k]
            history[k] = [val_item]

    # Process keys present only in dict one (keys removed in dict two).
    for k in only_one:
        if isinstance(one[k], dict):
            val_item = copy.copy(history_record)
            val_item['value'] = None
            val_item['diff'] = {'$delete': [k]}
            history[k] = [val_item]
        else:
            val_item = copy.copy(history_record)
            val_item['value'] = None
            val_item['diff'] = {'$delete': [k]}
            history[k] = [val_item]

    return history


def _keep_1_list(data: dict) -> dict:
    for k in data:
        if isinstance(data[k], dict):
            data[k] = _keep_1_list(data[k])
        if isinstance(data[k], list):
            l = data[k]
            if len(l) >= 1:
                data[k] = [l[0]]
            else:
                continue

            if isinstance(l[0], dict):
                data[k][0] = _keep_1_list(data[k][0])

    return data


def _fill_json(val: dict | list, mode: Literal['null', 'default'] = 'default') -> dict:
    if isinstance(val, dict):
        for k in val:
            if isinstance(val[k], dict):
                val[k] = _fill_json(val[k])
            elif isinstance(val[k], (list, tuple)):
                val[k] = _fill_json(list(val[k]))
            else:
                val[k] = None if mode == 'null' else type(val[k])()
    else:
        for k in range(len(val)):
            if isinstance(val[k], dict):
                val[k] = _fill_json(val[k])
            elif isinstance(val[k], (list, tuple)):
                val[k] = _fill_json(list(val[k]))
            else:
                val[k] = None if mode == 'null' else type(val[k])()

    return val


def extract_json(text):
    match = re.search(r"({.*?})", text, re.DOTALL)
    if match:
        json_str = match.group(1).strip()
        # TODO: seems like it breaks special symbols including ó or á in translations
        # json_str = json_str.replace("\\", "\\\\")
        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            return json.loads(text[text.index('{'):text.rindex('}') + 1])
    else:
        logger.warning("No JSON block found in the input text")
        return None


def extract_json_list(text):
    match = re.search(r"(\[.*?\])", text, re.DOTALL)
    if match:
        json_str = match.group(1).strip()
        # TODO: seems like it breaks special symbols including ó or á in translations
        # json_str = json_str.replace("\\", "\\\\")
        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            return json.loads(text[text.index('['):text.rindex(']') + 1])
    else:
        logger.warning("No JSON list block found in the input text")
        return None


def json_to_csv(json_data):
    flattened_data = flatten_json(json_data)

    df = pd.DataFrame(flattened_data, columns=['Name', 'Value'])
    csv_data = df.to_csv(index=False, sep=';')
    return csv_data


def json_to_xlsx(json_data):
    flattened_data = flatten_json(json_data)

    df = pd.DataFrame(flattened_data, columns=['Name', 'Value'])

    buf = io.BytesIO()
    # df.to_excel(buf, sheet_name='data', engine='xlsxwriter', index=False)
    writer = pd.ExcelWriter(buf)
    sheet_name = 'Data'
    df.to_excel(writer, sheet_name=sheet_name, index=False, engine='xlsxwriter')

    for column in df:
        column_width = max(df[column].astype(str).map(len).max(), len(column))
        col_idx = df.columns.get_loc(column)
        writer.sheets[sheet_name].set_column(col_idx, col_idx, column_width)

    writer.close()

    return buf.getvalue()


def dict_merge(
    dct_left,
    merge_dct,
    mode='overwrite',
    check_keys=False,
    copy_data=True,
    llm: BaseChatModel = None,
    verbose: bool = False,
):
    """
    Recursive dict merge. The method recurse down into dicts nested
    to an arbitrary depth, updating keys. The ``merge_dct`` is merged into ``dct_left``.

    Parameters
    ----------
    dct_left: dict
        Dictionary onto which the merge is executed

    merge_dct:dict
        Dictionary merged into dct

    mode:str, optional, default='safe'
        3 modes are allowed: "safe" or "overwrite" or "overwrite_append" or 'overwrite_llm'.
        "safe" will raise error if the 2 dicts have the same key and different values
        while "overwrite" will overwrite ``dct`` with the value of ``merge_dct``

    check_keys: bool
        Should the method check if keys from ``merge_dct`` are present in ``dct_left``
        and throw an error in case they are not

    copy_data: copy the source dict before editing or not. Default is True.
    llm: Use llm for smart list merging.
    verbose: log LLM usage or not

    Returns
    -------
    dict
        Merged dict
    """

    if not merge_dct:
        return dct_left

    if copy_data:
        dct = copy.deepcopy(dct_left)
    else:
        dct = dct_left

    if mode not in ["safe", "overwrite", "overwrite_append", "overwrite_llm"]:
        raise ValueError(f"dict_merge mode '{mode}' is not supported")

    for k, v in merge_dct.items():

        if k not in dct.keys() and check_keys:
            raise Exception(f"Cannot overlay non existing config item '{k}'")

        if k in dct and isinstance(dct[k], dict) and isinstance(merge_dct[k], dict):
            dct[k] = dict_merge(dct[k], merge_dct[k], mode, copy_data=False, check_keys=check_keys, llm=llm,
                                verbose=verbose)
        elif k not in dct and isinstance(merge_dct[k], dict):
            dct[k] = merge_dct[k]
        elif mode == 'overwrite_llm' and k in dct and isinstance(dct[k], list) and isinstance(merge_dct[k], list):
            prompt = MERGE_LISTS_PROMPT.format(one=json.dumps(dct[k], indent=2), two=json.dumps(merge_dct[k], indent=2))
            if verbose:
                logger.info(f"[MERGE] Input: {prompt}")
            llm_output = llm.invoke(prompt)
            if verbose:
                logger.info(f"[MERGE] Output: {llm_output.content}")
            dct[k] = extract_json_list(llm_output.content)
        elif mode == 'overwrite_append' and k in dct and isinstance(dct[k], list) and isinstance(merge_dct[k], list):
            if len(dct[k]) > 0:
                exceptions = list(NA_VALUES)
                need_replace = isinstance(dct[k][0], dict) and all(dct[k][0][kk] in exceptions for kk in dct[k][0])
                need_append = True
                if len(merge_dct[k]) > 0 and isinstance(merge_dct[k][0], dict) and isinstance(dct[k][0], dict):
                    # Use the keys from merge_dct[k][0] to check values, not dct[k][0]
                    need_append = not all(merge_dct[k][0].get(kk) in exceptions for kk in merge_dct[k][0])

                if dct[k][0] is None or need_replace:
                    dct[k] = merge_dct[k]
                else:
                    if need_append or not isinstance(merge_dct[k][0], dict):
                        dct[k] = dct[k] + merge_dct[k]
            else:
                dct[k] = dct[k] + merge_dct[k]
        elif mode == 'overwrite_append' and k in dct and isinstance(dct[k], list) and not isinstance(merge_dct[k], list):
            # Disallow overwrite list by a scalar
            pass
        else:
            if mode == "safe":

                if k in dct and dct[k] is not None and dct[k] != merge_dct[k]:
                    raise Exception(
                        f"Trying to overwrite parameter '{k}' of value '{dct[k]}' "
                        f"with value '{merge_dct[k]}'. Operation not allowed in 'safe' mode"
                    )
                else:
                    dct[k] = merge_dct[k]

            if mode == "overwrite" or mode == 'overwrite_append':
                is_list_right = isinstance(merge_dct.get(k), list)
                is_list_left = isinstance(dct.get(k), list)

                if is_list_left and is_list_right:
                    dct[k] = merge_dct[k]
                    continue

                # Check if value is a dict before doing membership test with NA_VALUES
                is_merge_val_dict = isinstance(merge_dct.get(k), dict)
                is_dct_val_dict = isinstance(dct.get(k), dict)

                # For dictionaries, we consider them "not in NA_VALUES" by default
                merge_val_not_in_na = is_merge_val_dict or (not is_list_right and merge_dct.get(k) not in NA_VALUES)
                dct_val_in_empty = is_dct_val_dict or (not is_list_left and dct.get(k) in EMPTY_VALUES)

                if merge_val_not_in_na or k not in dct or dct_val_in_empty:
                    if merge_dct[k] is not None or k not in dct:
                        dct[k] = merge_dct[k]

    return dct
