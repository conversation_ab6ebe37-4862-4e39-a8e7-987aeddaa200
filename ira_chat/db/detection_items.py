import sqlalchemy as sa
from sqlalchemy import delete, update, text
from sqlalchemy.future import select
from sqlalchemy.orm import load_only, make_transient

from ira_chat.db import base
from ira_chat.db import models
from ira_chat.utils import utils


@base.session_aware()
async def create_detection_item(values, session=None):
    return await base.create_model(models.DetectionItem, values, session=session)


@base.session_aware()
async def get_detection_items_count_ws(workspace_id: int, owner_id: int = None, detection_id: int = None, session=None):
    raw = 'select count(*) as count from detection_items where workspace_id = :workspace_id'
    params = {'workspace_id': workspace_id}
    if owner_id:
        params['owner_id'] = owner_id
        raw += ' AND owner_id = :owner_id'
    if detection_id:
        params['detection_id'] = detection_id
        raw += ' AND detection_id = :detection_id'

    sql = text(raw)
    res = await session.execute(sql, params)
    chat_count = res.scalar()

    return chat_count


@base.session_aware()
async def list_detection_items(
    owner_id: int = None,
    workspace_id: int = None,
    detection_id: int = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    skip_columns: list[str] = None,
    session=None,
):
    count = None
    offset = None
    if limit and page:
        count = await get_detection_items_count_ws(
            workspace_id=workspace_id, owner_id=owner_id, detection_id=detection_id
        )
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.DetectionItem)
    if owner_id:
        query = query.where(models.DetectionItem.owner_id == owner_id)
    if workspace_id is not None:
        query = query.where(models.DetectionItem.workspace_id == workspace_id)
    if detection_id is not None:
        query = query.where(models.DetectionItem.detection_id == detection_id)

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)
    if skip_columns:
        col_list = [
            getattr(models.DetectionItem, cname) for cname, col in models.DetectionItem.__table__.columns.items()
            if cname not in skip_columns
        ]
        query = query.options(load_only(*col_list))

    order_col = getattr(models.DetectionItem, order)
    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    objects = res.scalars().fetchall()
    [make_transient(obj) for obj in objects]
    return objects, count


@base.session_aware()
async def list_joined_detection_items(
    owner_id: int = None,
    workspace_id: int = None,
    detection_id: int = None,
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    skip_columns: list[str] = None,
    return_count=True,
    return_files=True,
    session=None,
):
    count = None
    offset = None
    limit_raw = ""
    if limit and page:
        if return_count:
            count = await get_detection_items_count_ws(
                workspace_id=workspace_id, owner_id=owner_id, detection_id=detection_id
            )
        limit, offset = utils.get_limit_offset(limit, page)
        limit_raw = "LIMIT :limit OFFSET :offset"

    if return_files:
        file_select = """
    detection_item_files.id AS detection_item_files_id,
    detection_item_files.name AS detection_item_files_name,
    detection_item_files.size AS detection_item_files_size,
    detection_item_files.status AS detection_item_files_status,
    detection_item_files.source_url AS detection_item_files_source_url,
    detection_item_files.owner_id AS detection_item_files_owner_id,
    detection_item_files.workspace_id AS detection_item_files_workspace_id,
    detection_item_files.created_at AS detection_item_files_created_at,
    detection_item_files.updated_at AS detection_item_files_updated_at,
    detection_item_files.detection_item_id AS detection_item_files_detection_item_id,
    """
        file_join = """
    LEFT JOIN detection_item_files
    ON limited_items.id = detection_item_files.detection_item_id"""
    else:
        file_select = ""
        file_join = ""

    # Just for sane check if col exists
    _ = getattr(models.DetectionItem, order)
    raw = f"""WITH limited_items AS (
    SELECT *
    FROM detection_items
    WHERE detection_items.workspace_id = :workspace_id
      AND detection_items.detection_id = :detection_id
    ORDER BY detection_items.{order}{" DESC" if desc else ""}
    {limit_raw}
)
SELECT
    limited_items.id AS detection_items_id,
    limited_items.title AS detection_items_title,
    limited_items.description AS detection_items_description,
    limited_items.input AS detection_items_input,
    limited_items.type AS detection_items_type,
    limited_items.status AS detection_items_status,
    limited_items.owner_id AS detection_items_owner_id,
    limited_items.workspace_id AS detection_items_workspace_id,
    limited_items.detection_id AS detection_items_detection_id,
    limited_items.created_at AS detection_items_created_at,
    limited_items.updated_at AS detection_items_updated_at,
    {file_select}
    detection_item_outputs.id AS detection_item_outputs_id,
    detection_item_outputs.output AS detection_item_outputs_output,
    detection_item_outputs.output_file_id AS detection_item_outputs_output_file_id,
    detection_item_outputs.rating AS detection_item_outputs_rating,
    detection_item_outputs.note AS detection_item_outputs_note,
    detection_item_outputs.status AS detection_item_outputs_status,
    detection_item_outputs.workspace_id AS detection_item_outputs_workspace_id,
    detection_item_outputs.detection_id AS detection_item_outputs_detection_id,
    detection_item_outputs.created_at AS detection_item_outputs_created_at,
    detection_item_outputs.updated_at AS detection_item_outputs_updated_at,
    detection_item_outputs.detection_item_id AS detection_item_outputs_detection_item_id

FROM limited_items
{file_join}
LEFT JOIN detection_item_outputs
    ON limited_items.id = detection_item_outputs.detection_item_id
ORDER BY limited_items.{order}{" DESC" if desc else ""};
    """
    sql = text(raw)
    params = {'limit': limit, 'offset': offset, 'workspace_id': workspace_id, 'detection_id': detection_id}

    if return_files:
        model_select = (models.DetectionItem, models.DetectionItemFile, models.DetectionItemOutput)
    else:
        model_select = (models.DetectionItem, models.DetectionItemOutput)

    query = select(*model_select).from_statement(sql)
    res = await session.execute(query, params)
    res_all = res.fetchall()
    # Rows
    # [(det_item, det_file, det_output)]

    results = list(zip(*res_all))
    # det_items, det_files, det_outputs = zip(*res_all)
    if not results:
        results = [[]] * len(model_select)
    if return_count:
        results.append(count)

    results[0] = list(dict.fromkeys(results[0]))
    results[1] = set([file for file in results[1] if file])
    if len(model_select) > 2:
        # Delete all None outputs
        results[2] = [out for out in results[2] if out]
    return results


@base.session_aware()
async def get_detection_item_by_id(id: int, session=None):
    return await base.get_by_id(models.DetectionItem, id, session=session)


@base.session_aware()
async def update_detection_item(detection, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.DetectionItem).where(models.DetectionItem.id == detection.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    detection.update(new_values)
    return detection


@base.session_aware()
async def update_detection_items(detection_id: int, new_values, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.DetectionItem).where(models.DetectionItem.detection_id == detection_id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)


@base.session_aware()
async def delete_detection_item(id: int, session=None):
    delete_q = delete(models.DetectionItem).where(models.DetectionItem.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_detection_items(detection_id: int, session=None):
    delete_q = delete(models.DetectionItem).where(models.DetectionItem.detection_id == detection_id)
    await session.execute(delete_q)


@base.session_aware()
async def get_detection_item_count(workspace_ids: list, session=None):
    sql = text(
        'select count(*) as count from detection_items '
        'where workspace_id in :workspace_ids'
    )
    params = {
        'workspace_ids': workspace_ids,
    }
    sql = sql.bindparams(sa.bindparam("workspace_ids", expanding=True))

    res = await session.execute(sql, params)
    count = res.scalar()

    return count


@base.session_aware()
async def get_detection_item_count_one(detection_id: int, session=None):
    sql = text(
        'select count(*) as count from detection_items '
        'where detection_id = :id'
    )
    params = {
        'id': detection_id,
    }

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
