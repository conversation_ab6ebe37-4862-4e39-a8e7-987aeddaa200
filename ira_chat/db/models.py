import datetime
import json

import sqlalchemy as sa
from sqlalchemy import types as st
from sqlalchemy.dialects.postgresql import JSON as PostgresJSON
from sqlalchemy.dialects.postgresql import JSON<PERSON> as PostgresJSONB

from ira_chat.db import base
from ira_chat.utils import utils, blowfish_utils
from ira_chat.utils.utils import build_status

ROLE_USER = 'user'
ROLE_NOT_SET = 'not_set'
ROLE_SYSTEM = 'system'
ROLE_SERVICE = 'service'
ROLE_AI = 'ai'

STATUS_CREATED = 'CREATED'
STATUS_UPDATED = 'UPDATED'
STATUS_PROCESSING = 'PROCESSING'
STATUS_PREPROCESSING = 'PREPROCESSING'
STATUS_ERROR = 'ERROR'
STATUS_ERROR_REASON = 'ERROR_REASON'
STATUS_SUCCESS = 'SUCCESS'
STATUS_GENERATING_QUESTION = 'GENERATING_QUESTION'
STATUS_PROCESSING_META = 'PROCESSING_METADATA'
STATUS_PROCESSING_CHUNK_META = 'PROCESSING_CHUNK_METADATA'
STATUS_DELETED = 'DELETED'
STATUS_DELETING = 'DELETING'

METRIC_MESSAGE = 'message'
METRIC_MESSAGE_DELETED = 'message_deleted'
METRIC_DETECTION_ITEM = 'detection_item'
METRIC_EXTRACTOR_ITEM = 'extractor_item'
METRIC_EXTRACTOR_RESULT = 'extractor_result'
METRIC_REQUEST = 'request'
METRIC_ERROR = 'error'
METRIC_RESPONSE_TIME = 'response_time'
METRIC_MESSAGE_RESPONSE_TIME = 'msg_response_time'
METRIC_INPUT_TOKENS = 'input_tokens'
METRIC_OUTPUT_TOKENS = 'output_tokens'
METRIC_TOKENS_SECOND = 'tokens_per_second'
METRIC_COST_DOLLARS = 'cost_dollars'
METRIC_CHAT_CREATED = 'chat_created'
METRIC_CHAT_DELETED = 'chat_deleted'
METRIC_SENTIMENT = 'sentiment'
METRIC_LANGUAGE_MATCH = 'language_match'
METRIC_CORRECTNESS = 'correctness'
METRIC_COHERENCE = 'coherence'
METRIC_FAITHFULNESS = 'faithfulness'
METRIC_RELEVANCE = 'answer_relevancy'
METRIC_CONTEXT_UTIL = 'context_utilization'
METRIC_WEIGHTED_EVAL = 'weighted_evaluation'
METRIC_RATING_VALUE = 'rating_value'
METRIC_RATING_EVENT = 'rating_event'
METRIC_GRADE_EVENT = 'grade_event'
METRIC_OFF_TOPIC_EVENT = 'off-topic no answer'
METRIC_EXCLUDED_ANSWER_EVENT = 'excluded_answer'
METRIC_NO_ANSWER_EVENT = 'no_answer'
METRIC_CORRECT_ANSWER = 'correct_answer'
METRIC_PARTIAL_ANSWER = 'partial_answer'
METRIC_INCORRECT_ANSWER = 'incorrect_answer'
METRIC_CORRECTNESS_GRADE = 'correctness_grade'
METRIC_SYSTEM_CORRECTNESS = 'system_correctness'
METRIC_CORRECTED_ANSWER = 'corrected_answer'

METRIC_GROUP_EVALUATION = 'Evaluation metrics'
METRIC_GROUP_FEEDBACK = 'Feedback metrics'
METRIC_GROUP_RATING = 'Rating'
METRIC_GROUP_EVENTS = 'Events'
METRIC_GROUP_LLM_EVENTS = 'LLM Events'
METRIC_GROUP_ITEMS = 'Items metrics'
METRIC_GROUP_BASE = 'Base metrics'
METRIC_GROUP_LATENCIES = 'Latency metrics'
METRIC_GROUP_COST = 'Cost'
METRIC_GROUP_TOKENS = 'Tokens metrics'
METRIC_GROUP_COMBINED = 'Combined metrics'
METRIC_GROUP_ERROR = 'Error metrics'

APP_TYPE_DETECTION = 'detection'
APP_TYPE_EXTRACTOR = 'extractor'
APP_TYPE_EXTRACTOR_FILE = 'extractor_file'
APP_TYPE_EXTRACTOR_REQ = 'extractor_request'
APP_TYPE_CHAT = 'chat'
APP_TYPE_DATASET_FILE = 'dataset_file'

ACCESS_TYPE_MEMBERS = 'members'
ACCESS_TYPE_AUTHENTICATED = 'authenticated'
ACCESS_TYPE_PUBLIC = 'public'
ACCESS_TYPES_ANY = {ACCESS_TYPE_AUTHENTICATED, ACCESS_TYPE_PUBLIC}
WS_ACCESS_TYPES = {ACCESS_TYPE_MEMBERS, ACCESS_TYPE_AUTHENTICATED, ACCESS_TYPE_PUBLIC}

DATASET_SCOPE_GENERAL = 'general'
DATASET_SCOPE_EXTRACTOR = 'extractor'


class SourceType:
    FILE = 'file'
    CORRECTED_TXT = 'corrected_file_txt'


class GradeSelector:
    CORRECT_ANSWER = 'Correct answer'
    PARTIAL_ANSWER = 'Partial answer'
    INCORRECT_ANSWER = 'Incorrect answer'
    NO_ANSWER_EXCLUDED = 'Excluded answer'
    NO_ANSWER_COMPLETELY = 'No answer'
    OFF_TOPIC_NO_ANSWER = 'Off-topic no answer'


TOKEN_WORKSPACE = 'workspace'
TOKEN_USER = 'user'

DETECTION_PRESCRIPTION = 'prescription'
DETECTION_MEDLIST = 'medlist'


class NotSet:
    pass


def now():
    return datetime.datetime.now(datetime.timezone.utc)


class Session(base.ModelBase):
    __tablename__ = 'sessions'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_uuid
    )
    ttl = sa.Column(sa.DateTime(timezone=True), nullable=True)
    admin = sa.Column(st.Boolean())
    user_id = sa.Column(st.BigInteger, nullable=False, index=True)
    extra = sa.Column(st.Text())

    def to_dict(self):
        d = super(Session, self).to_dict()
        if not self.admin:
            del d['admin']
        return d


class User(base.ModelBase):
    __tablename__ = 'users'

    id = sa.Column(
        sa.BigInteger(),
        primary_key=True,
    )
    name = sa.Column(sa.String(200))
    info = sa.Column(sa.Text())
    login = sa.Column(sa.String(200))
    password = sa.Column(sa.String(200))
    confirmed = sa.Column(sa.Boolean())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    last_seen_at = sa.Column(sa.DateTime(timezone=True), nullable=True)
    last_login_at = sa.Column(sa.DateTime(timezone=True), nullable=True)
    last_activity_at = sa.Column(sa.DateTime(timezone=True), nullable=True)

    def to_dict(self):
        d = super(User, self).to_dict()
        del d['password']

        d['info'] = utils.maybe_dict(d['info'])
        return d


class UserConfirm(base.ModelBase):
    __tablename__ = 'user_confirms'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
    )
    user_id = sa.Column(sa.BigInteger())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    expiry_time = sa.Column(sa.DateTime(timezone=True), nullable=False)


class UserInvite(base.ModelBase):
    __tablename__ = 'user_invites'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
    )
    from_user_id = sa.Column(sa.BigInteger())
    to_user_id = sa.Column(sa.BigInteger())
    to_user_login = sa.Column(sa.String(200))
    to_workspace_id = sa.Column(sa.Integer())
    to_group_id = sa.Column(sa.Integer())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    expiry_time = sa.Column(sa.DateTime(timezone=True), nullable=False)


class UserToken(base.ModelBase):
    __tablename__ = 'user_tokens'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    user_id = sa.Column(sa.BigInteger())
    token = sa.Column(sa.String(36), index=True)
    workspace_id = sa.Column(sa.Integer(), index=True)
    username = sa.Column(sa.String(200))
    description = sa.Column(sa.String(255))
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    expiry_time = sa.Column(sa.DateTime(timezone=True), nullable=False)


class UserServiceAccount(base.ModelBase):
    __tablename__ = 'user_service_accounts'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    user_id = sa.Column(sa.BigInteger(), index=True)
    service = sa.Column(sa.String(200), index=True)
    service_id = sa.Column(sa.String(200))
    service_name = sa.Column(sa.String(200))
    service_picture = sa.Column(sa.String(255))
    token = sa.Column(sa.Text())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def get_token(self):
        if self.token:
            if isinstance(self.token, dict):
                return self.token
            return blowfish_utils.decrypt_dict(self.token)
        else:
            return {}

    def set_token(self, token: dict):
        self.token = blowfish_utils.encrypt_dict(token)


class Token(base.ModelBase):
    __tablename__ = 'tokens'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_uuid
    )
    owner_id = sa.Column(sa.BigInteger())
    object_id = sa.Column(sa.BigInteger(), index=True)
    object_type = sa.Column(sa.String(30), index=True)
    # If type == user then permissions should be inherited from the user and must be NULL here
    permissions = sa.Column(sa.BigInteger(), nullable=True)
    org_permissions = sa.Column(sa.BigInteger(), nullable=True)
    description = sa.Column(sa.String(255))
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    expiry_time = sa.Column(sa.DateTime(timezone=True), nullable=False)


class Workspace(base.ModelBase):
    __tablename__ = 'workspaces'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    org_id = sa.Column(sa.Integer())
    name = sa.Column(sa.String(200))
    display_name = sa.Column(sa.String(200))
    description = sa.Column(sa.String(255))
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    config = sa.Column(PostgresJSON())
    status = sa.Column(PostgresJSON(), default=build_status(STATUS_SUCCESS))
    access_type = sa.Column(sa.VARCHAR(30), default=ACCESS_TYPE_MEMBERS)

    def to_dict(self, exclude_config=False):
        d = super(Workspace, self).to_dict()
        d['config'] = self.get_config()
        if exclude_config:
            del d['config']
        return d

    def get_dataset_index(self):
        return (self.get_config().get('index') or '/').split('/')

    def get_config(self):
        return self.config.copy() or {}


class Organization(base.ModelBase):
    __tablename__ = 'organizations'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    name = sa.Column(sa.String(200))
    display_name = sa.Column(sa.String(200))
    description = sa.Column(sa.String(255))
    config = sa.Column(st.Text())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    deleted_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=None)

    def to_dict(self):
        d = super(Organization, self).to_dict()
        if d['config']:
            d['config'] = self.get_config()
        return d

    def get_config(self):
        if self.config:
            if isinstance(self.config, dict):
                return self.config
            return blowfish_utils.decrypt_dict(self.config)
        else:
            return {}

    def set_config(self, config: dict):
        self.config = blowfish_utils.encrypt_dict(config)


class OrganizationDomain(base.ModelBase):
    __tablename__ = 'organization_domains'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    domain = sa.Column(sa.String(200), nullable=False, unique=True)
    org_id = sa.Column(sa.Integer(), nullable=False, index=True)


class OrganizationWebhook(base.ModelBase):
    __tablename__ = 'organization_webhooks'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    key = sa.Column(sa.String(36), nullable=False, default=utils.generate_unicode_uuid)
    org_id = sa.Column(sa.Integer(), nullable=False, index=True)
    description = sa.Column(st.Text())
    config = sa.Column(PostgresJSON())
    url = sa.Column(st.Text())
    headers = sa.Column(st.Text())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self, show_key=False):
        d = super().to_dict()
        if not show_key:
            del d['key']
        d['headers'] = self.get_headers()
        return d

    def get_headers(self):
        if self.headers:
            if isinstance(self.headers, dict):
                return self.headers
            return utils.maybe_dict(self.headers)
        else:
            return {}


class OrganizationWebhookItem(base.ModelBase):
    __tablename__ = 'organization_webhook_items'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )

    webhook_id = sa.Column(sa.Integer(), nullable=False, index=True)
    headers = sa.Column(st.Text())
    payload = sa.Column(st.Text())
    status_code = sa.Column(st.Integer())
    response_headers = sa.Column(st.Text())
    response = sa.Column(st.Text())
    error = sa.Column(st.Text(), default=None)
    sent_at = sa.Column(sa.DateTime(timezone=True), nullable=False, default=now)

    def to_dict(self, show_key=False):
        d = super().to_dict()
        d['headers'] = utils.maybe_dict(d['headers'])
        d['response_headers'] = utils.maybe_dict(d['response_headers'])
        d['payload'] = utils.maybe_dict(d['payload'])
        return d


class OrganizationUser(base.ModelBase):
    __tablename__ = 'organization_users'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    org_id = sa.Column(sa.Integer(), index=True)
    user_id = sa.Column(sa.BigInteger(), index=True)
    user_login = sa.Column(sa.String(200))
    # permissions = sa.Column(sa.BigInteger())
    role_id = sa.Column(sa.Integer())
    confirmed = sa.Column(sa.Boolean())


class Group(base.ModelBase):
    __tablename__ = 'groups'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    workspace_id = sa.Column(sa.Integer(), index=True)
    name = sa.Column(sa.String(63))
    permissions = sa.Column(sa.BigInteger())


class GroupUser(base.ModelBase):
    __tablename__ = 'group_users'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    workspace_id = sa.Column(sa.Integer(), index=True)
    user_id = sa.Column(sa.BigInteger(), index=True)
    user_login = sa.Column(sa.String(200))
    group_id = sa.Column(sa.Integer(), index=True)
    confirmed = sa.Column(sa.Boolean())


class Metric(base.ModelBase):
    __tablename__ = 'metrics'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    value = sa.Column(sa.Double())
    type = sa.Column(sa.String(20), index=True)
    workspace_id = sa.Column(sa.Integer(), index=True)
    org_id = sa.Column(sa.Integer(), index=True)
    app_type = sa.Column(sa.String(20), index=True)
    app_id = sa.Column(sa.Integer(), index=True)
    object_id = sa.Column(sa.String(36), index=True)
    extra = sa.Column(PostgresJSON())
    tag = sa.Column(sa.String(20), index=True)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)

    __table_args__ = (
        sa.Index("idx_metrics_workspace_app", "workspace_id", "app_type", "app_id", id.desc()),
        sa.Index("idx_metrics_ws_app_type_app_id_object", "workspace_id", "app_type", "app_id", "object_id"),
        sa.Index("idx_metrics_ws_type_updated_at_app_id", "workspace_id", "type", "updated_at", "app_id"),
        sa.Index("ix_metrics_tag_null_filtered", "workspace_id", "updated_at", "type", "value", postgresql_where=tag.is_(None)),
    )


class Chat(base.ModelBase):
    __tablename__ = 'chats'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    title = sa.Column(st.VARCHAR(length=255))
    description = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False)
    workspace_id = sa.Column(st.Integer, index=True)
    config = sa.Column(PostgresJSON())
    meta = sa.Column(PostgresJSON())
    status = sa.Column(PostgresJSON(), default=build_status(STATUS_SUCCESS))
    metrics = sa.Column(PostgresJSON(), default={})
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def get_dataset_index(self):
        return (self.get_config().get('index') or '/').split('/')

    def to_dict(self):
        d = super(Chat, self).to_dict()
        d['config'] = self.get_config()
        return d

    def get_config(self):
        if self.config:
            return self.config.copy()
        else:
            return {}


class Detection(base.ModelBase):
    __tablename__ = 'detections'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    title = sa.Column(st.String(length=255), nullable=True)
    description = sa.Column(st.Text(), nullable=True)
    type = sa.Column(st.String(length=30))
    owner_id = sa.Column(st.BigInteger, nullable=False, index=True)
    workspace_id = sa.Column(st.Integer, index=True)
    config = sa.Column(st.JSON(), nullable=True)
    meta = sa.Column(PostgresJSON())
    metrics = sa.Column(PostgresJSON(), nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self):
        d = super(Detection, self).to_dict()
        return d

    def get_config(self):
        if self.config:
            return self.config
        else:
            return {}


class DetectionItem(base.ModelBase):
    __tablename__ = 'detection_items'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    title = sa.Column(st.String(length=255), nullable=True)
    description = sa.Column(st.Text(), nullable=True)
    input = sa.Column(st.Text())

    type = sa.Column(st.String(length=30))
    status = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False, index=True)
    workspace_id = sa.Column(st.Integer, index=True)
    detection_id = sa.Column(st.Integer, index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    __table_args__ = (
        sa.Index("idx_detection_item_ws_det_id", "workspace_id", "detection_id"),
    )

    def to_dict(self):
        d = super(DetectionItem, self).to_dict()

        return d


class DetectionItemOutput(base.ModelBase):
    __tablename__ = 'detection_item_outputs'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    # Result fields
    output = sa.Column(st.Text(), nullable=True)
    output_file_id = sa.Column(st.String(36), nullable=True)
    rating = sa.Column(sa.Integer(), nullable=True)
    note = sa.Column(st.Text(), nullable=True)

    status = sa.Column(st.Text())
    workspace_id = sa.Column(st.Integer, index=True)
    detection_id = sa.Column(st.Integer, index=True)
    detection_item_id = sa.Column(st.Integer, index=True)
    # config = sa.Column(st.JSON(), nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self, skip_ids=False):
        d = super(DetectionItemOutput, self).to_dict()

        if skip_ids:
            del d['workspace_id']
            del d['detection_id']
            del d['detection_item_id']

        if hasattr(self, 'output_file'):
            if self.output_file:
                d['output_file'] = self.output_file.to_dict(skip_ids=True)
            else:
                d['output_file'] = self.output_file
        return d


class DetectionItemFile(base.ModelBase):
    __tablename__ = 'detection_item_files'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_uuid
    )
    detection_item_id = sa.Column(st.Integer, index=True)
    name = sa.Column(st.String(length=255))
    size = sa.Column(st.Integer())
    status = sa.Column(st.String(20), nullable=True)
    source_url = sa.Column(st.Text(), nullable=True)
    owner_id = sa.Column(st.BigInteger, nullable=False)
    workspace_id = sa.Column(st.Integer, index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self, skip_ids=False):
        d = super(DetectionItemFile, self).to_dict()
        if skip_ids:
            del d['detection_item_id']
            del d['owner_id']
            del d['workspace_id']

        return d


class ChatMessage(base.ModelBase):
    __tablename__ = 'chat_messages'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    chat_id = sa.Column(sa.Integer(), index=True)
    content = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False)
    role = sa.Column(st.VARCHAR(length=10))
    status = sa.Column(st.VARCHAR(length=20), index=True)
    tags = sa.Column(PostgresJSONB(), nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    __table_args__ = (
        sa.Index("idx_chat_messages_tags", "tags", postgresql_using="gin", postgresql_ops={"tags": "jsonb_path_ops"}),
    )


class ChatMessageOutput(base.ModelBase):
    __tablename__ = 'chat_message_outputs'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    chat_id = sa.Column(sa.Integer(), index=True)
    chat_message_id = sa.Column(sa.Integer(), index=True)
    content = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False)
    role = sa.Column(st.VARCHAR(length=10))
    status = sa.Column(st.VARCHAR(length=20), index=True)
    context = sa.Column(sa.JSON(none_as_null=True), nullable=True)
    rating = sa.Column(sa.Integer())
    note = sa.Column(st.Text(), nullable=True)
    grade_selector = sa.Column(st.String(63), nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self, skip_ids=False):
        d = super(ChatMessageOutput, self).to_dict()
        # if d['context']:
        #     d['context'] = utils.maybe_dict(d['context'])

        if skip_ids:
            d.pop('owner_id')
            d.pop('chat_id')

        return d


class ChatSuggestion(base.ModelBase):
    __tablename__ = 'chat_suggestions'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    chat_id = sa.Column(sa.Integer(), index=True)
    message_id = sa.Column(sa.Integer(), nullable=False, index=True)
    content = sa.Column(st.Text())
    status = sa.Column(st.VARCHAR(length=20))
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self, skip_ids=False):
        d = super(ChatSuggestion, self).to_dict()

        if skip_ids:
            del d['chat_id']
            del d['message_id']

        return d


class ProvidedAnswer(base.ModelBase):
    __tablename__ = 'provided_answers'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    workspace_id = sa.Column(st.Integer, index=True)
    app_id = sa.Column(sa.Integer(), index=True)
    object_id = sa.Column(sa.Integer(), nullable=False, index=True)
    question = sa.Column(st.Text())
    answer = sa.Column(st.Text())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class File(base.ModelBase):
    __tablename__ = 'files'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    name = sa.Column(st.String(length=255))
    size = sa.Column(st.Integer())
    status = sa.Column(st.String(length=511))
    description = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False)
    workspace_id = sa.Column(st.Integer, index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self):
        d = super(File, self).to_dict()
        d['status'] = json.loads(d['status'])
        return d


class Dataset(base.ModelBase):
    __tablename__ = 'datasets'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_ulid
    )
    name = sa.Column(st.String(length=255))
    display_name = sa.Column(st.String(length=255))
    total_size = sa.Column(st.BigInteger(), default=0)
    file_count = sa.Column(st.Integer(), default=0)
    status = sa.Column(PostgresJSON())
    config = sa.Column(PostgresJSON(), default={})
    scope = sa.Column(st.String(20), default=DATASET_SCOPE_GENERAL, index=True)
    description = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False)
    org_id = sa.Column(st.Integer, index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    __table_args__ = (
        sa.Index("idx_datasets_org_name", "org_id", "name", unique=True),
    )

    def to_dict(self):
        d = super(Dataset, self).to_dict()
        return d

    def get_config(self):
        if self.config:
            return self.config.copy()
        else:
            return {}


class DatasetFile(base.ModelBase):
    __tablename__ = 'dataset_files'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_ulid
    )
    name = sa.Column(st.String(length=255))
    size = sa.Column(st.Integer())
    status = sa.Column(st.JSON())
    description = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False)
    dataset_id = sa.Column(sa.String(36), index=True)
    org_id = sa.Column(st.Integer, index=True)
    hash = sa.Column(st.String(64), index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    source_type = sa.Column(st.String(40), nullable=True, default=SourceType.FILE, index=True)

    meta = sa.Column(PostgresJSON(), nullable=True, default={})

    def to_dict(self):
        d = super(DatasetFile, self).to_dict()
        return d


class DatasetIndex(base.ModelBase):
    __tablename__ = 'dataset_indexes'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_ulid
    )
    name = sa.Column(st.String(length=255))
    display_name = sa.Column(st.String(length=255))
    total_size = sa.Column(st.BigInteger(), default=0)
    file_count = sa.Column(st.Integer(), default=0)
    vector_count = sa.Column(st.Integer(), default=0)
    status = sa.Column(st.JSON())
    description = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False)
    org_id = sa.Column(st.Integer, index=True)
    dataset_id = sa.Column(sa.String(36), index=True)
    embedding_provider = sa.Column(sa.String(255))
    config = sa.Column(sa.JSON())
    locked_at = sa.Column(sa.DateTime(timezone=True), nullable=True)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    __table_args__ = (
        sa.Index("idx_dataset_indexes_dataset_name", "dataset_id", "name", unique=True),
    )

    def to_dict(self):
        d = super(DatasetIndex, self).to_dict()
        return d

    def is_locked(self):
        return self.locked_at is not None

    def get_config(self):
        if self.config:
            return self.config.copy()
        else:
            return {}


class DatasetFileEmbeddings(base.ModelBase):
    __tablename__ = 'dataset_file_embeddings'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_ulid
    )
    dataset_file_id = sa.Column(sa.String(36), index=True)
    dataset_file_hash = sa.Column(st.String(64))
    index_id = sa.Column(sa.String(36), index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class DatasetFileDocument(base.ModelBase):
    __tablename__ = 'dataset_file_documents'

    id = sa.Column(
        sa.String(length=36),
        primary_key=True,
    )
    dataset_file_id = sa.Column(sa.String(36), index=True)
    index_id = sa.Column(sa.String(36), index=True)
    content = sa.Column(st.Text())
    # Cannot use 'metadata' field name due to sqlalchemy
    doc_metadata = sa.Column(st.JSON())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def get_doc_metadata(self):
        if self.doc_metadata:
            meta = self.doc_metadata.copy()
            meta['_id'] = self.id
            return meta
        else:
            return {}


class FileEmbeddings(base.ModelBase):
    __tablename__ = 'file_embeddings'

    id = sa.Column(
        sa.String(length=60),
        primary_key=True,
    )
    file_id = sa.Column(sa.Integer(), index=True)
    extractor_file_id = sa.Column(sa.String(36), index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class Document(base.ModelBase):
    __tablename__ = 'documents'

    id = sa.Column(
        sa.String(length=60),
        primary_key=True,
    )
    file_id = sa.Column(sa.Integer(), index=True)
    content = sa.Column(st.Text())
    # Cannot use 'metadata' field name due to sqlalchemy
    doc_metadata = sa.Column(st.Text())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def get_doc_metadata(self):
        if self.doc_metadata:
            meta = json.loads(self.doc_metadata)
            meta['_id'] = self.id
            return meta
        else:
            return {}


class Extractor(base.ModelBase):
    __tablename__ = 'extractors'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    workspace_id = sa.Column(st.Integer, index=True)
    owner_id = sa.Column(st.BigInteger, nullable=False, index=True)
    title = sa.Column(st.String(length=255), nullable=True)
    description = sa.Column(st.Text(), nullable=True)

    status = sa.Column(st.JSON())
    has_updates = sa.Column(sa.Boolean())
    hidden = sa.Column(sa.Boolean(), default=False)

    config = sa.Column(st.JSON(), nullable=True)
    meta = sa.Column(PostgresJSON())
    auto_dataset_id = sa.Column(sa.String(36))
    schema = sa.Column(st.Text(), nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self):
        d = super(Extractor, self).to_dict()

        d['data_schema'] = utils.maybe_dict(d.pop('schema', None))

        return d

    def get_config(self):
        if self.config:
            return self.config.copy()
        else:
            return {}

    def get_dataset_index(self):
        return (self.get_config().get('index') or '/').split('/')

    def get_extended_config(self):
        ext_config = self.get_config()
        # ext_config['index_suffix'] = f'extractor-{self.id}'
        return ext_config


class ExtractorResult(base.ModelBase):
    __tablename__ = 'extractor_results'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    # Result fields
    output = sa.Column(st.Text(), nullable=True)
    rating = sa.Column(sa.Integer(), nullable=True)
    note = sa.Column(st.Text(), nullable=True)

    status = sa.Column(st.JSON())
    owner_id = sa.Column(st.BigInteger, nullable=False, index=True)
    workspace_id = sa.Column(st.Integer, index=True)
    extractor_id = sa.Column(st.Integer, index=True)
    # config = sa.Column(st.JSON(), nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self, skip_ids=False):
        d = super(ExtractorResult, self).to_dict()
        # d['config'] = self.get_config()
        # d['status'] = utils.maybe_dict(d['status'])

        if skip_ids:
            del d['workspace_id']
            del d['extractor_id']
        return d

    # def get_config(self):
    #     if self.config:
    #         return self.config.copy()
    #     else:
    #         return {}


class ExtractorItem(base.ModelBase):
    __tablename__ = 'extractor_items'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    title = sa.Column(st.String(length=255), nullable=True)
    description = sa.Column(st.Text(), nullable=True)
    input = sa.Column(st.Text())

    status = sa.Column(st.Text())
    owner_id = sa.Column(st.BigInteger, nullable=False, index=True)
    workspace_id = sa.Column(st.Integer, index=True)
    extractor_id = sa.Column(st.Integer, index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self):
        d = super(ExtractorItem, self).to_dict()

        return d


class ExtractorItemOutput(base.ModelBase):
    __tablename__ = 'extractor_item_outputs'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    # Result fields
    output = sa.Column(st.Text(), nullable=True)
    rating = sa.Column(sa.Integer(), nullable=True)
    note = sa.Column(st.Text(), nullable=True)

    status = sa.Column(st.Text())
    workspace_id = sa.Column(st.Integer, index=True)
    extractor_id = sa.Column(st.Integer, index=True)
    extractor_item_id = sa.Column(st.Integer, index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self, skip_ids=False):
        d = super(ExtractorItemOutput, self).to_dict()
        # if d['config']:
        #     d['config'] = json.loads(d['config'])

        if skip_ids:
            del d['workspace_id']
            del d['extractor_id']
            del d['extractor_item_id']
        return d


class ExtractorFile(base.ModelBase):
    __tablename__ = 'extractor_files'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_uuid
    )
    extractor_id = sa.Column(st.Integer, index=True)
    name = sa.Column(st.String(length=255))
    size = sa.Column(st.Integer())
    status = sa.Column(st.JSON(), nullable=True)
    meta = sa.Column(PostgresJSON(), nullable=True)
    owner_id = sa.Column(st.BigInteger, nullable=False)
    workspace_id = sa.Column(st.Integer, index=True)
    hash = sa.Column(st.String(64), index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self):
        d = super(ExtractorFile, self).to_dict()
        # d['status'] = json.loads(d['status'])
        if self.meta:
            d['meta'] = self.meta.copy()
        return d


class Timeline(base.ModelBase):
    __tablename__ = 'timelines'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    workspace_id = sa.Column(st.Integer, index=True)
    owner_id = sa.Column(st.BigInteger, nullable=False, index=True)
    title = sa.Column(st.String(length=255), nullable=True)
    description = sa.Column(st.Text(), nullable=True)

    status = sa.Column(st.JSON())
    has_updates = sa.Column(sa.Boolean())

    config = sa.Column(st.JSON(), nullable=True)
    meta = sa.Column(PostgresJSON())
    data_schema = sa.Column(st.Text(), nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self):
        d = super(Timeline, self).to_dict()
        # d['config'] = utils.maybe_dict(d['config'])

        d['data_schema'] = utils.maybe_dict(d['data_schema'])
        # d['status'] = utils.maybe_dict(d['status'])

        return d

    def get_config(self):
        if self.config:
            return self.config.copy()
        else:
            return {}

    def get_extended_config(self):
        ext_config = self.get_config()
        ext_config['index_suffix'] = f'timeline-{self.id}'
        return ext_config


class TimelinePoint(base.ModelBase):
    __tablename__ = 'timeline_points'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    workspace_id = sa.Column(st.Integer, index=True)
    timeline_id = sa.Column(st.Integer, index=True)
    extractor_id = sa.Column(st.Integer, index=True)
    owner_id = sa.Column(st.BigInteger, nullable=False, index=True)
    title = sa.Column(st.String(length=255), nullable=True)
    description = sa.Column(st.Text(), nullable=True)
    status = sa.Column(PostgresJSON())
    auto_process = sa.Column(st.Boolean())

    # Result fields
    output = sa.Column(st.Text(), nullable=True)
    rating = sa.Column(sa.Integer(), nullable=True)
    note = sa.Column(st.Text(), nullable=True)

    date = sa.Column(sa.Date(), nullable=False, index=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self):
        d = super(TimelinePoint, self).to_dict()
        d['status'] = utils.maybe_dict(d['status'])
        d['output'] = utils.maybe_dict(d['output'])
        if 'auto_process' in d:
            del d['auto_process']

        return d


class TimelinePointChange(base.ModelBase):
    __tablename__ = 'timeline_point_changes'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    workspace_id = sa.Column(st.Integer, index=True)
    timeline_id = sa.Column(st.Integer, index=True)
    point_id = sa.Column(st.Integer, index=True)
    owner_id = sa.Column(st.BigInteger, nullable=False, index=True)

    data = sa.Column(st.Text(), nullable=True)
    diff = sa.Column(st.Text(), nullable=True)
    description = sa.Column(st.Text(), nullable=True)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def to_dict(self):
        d = super(TimelinePointChange, self).to_dict()
        d['data'] = utils.maybe_dict(d['data'])
        d['diff'] = utils.maybe_dict(d['diff'])

        return d


# class TimelinePointResult(base.ModelBase):
#     __tablename__ = 'timeline_point_results'
#
#     id = sa.Column(
#         sa.Integer(),
#         primary_key=True,
#     )
#     # Result fields
#     output = sa.Column(st.Text(), nullable=True)
#     rating = sa.Column(sa.Integer(), nullable=True)
#     note = sa.Column(st.Text(), nullable=True)
#
#     status = sa.Column(st.Text())
#     owner_id = sa.Column(st.BigInteger, nullable=False, index=True)
#     workspace_id = sa.Column(st.Integer, index=True)
#     timeline_id = sa.Column(st.Integer, index=True)
#     point_id = sa.Column(st.Integer, index=True)
#     created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)
#     updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
#
#     def to_dict(self, skip_ids=False):
#         d = super(TimelinePointResult, self).to_dict()
#         d['status'] = utils.maybe_dict(d['status'])
#
#         if skip_ids:
#             del d['workspace_id']
#             del d['timeline_id']
#             del d['point_id']
#         return d
