import copy
import datetime
import json

from ira_chat.db.base import date_to_string
from ira_chat.utils import json_utils

schema = json.loads(open('test-scripts/data_schema.json').read())
result = json.loads(open('test-scripts/data1.json').read())
result2 = json.loads(open('test-scripts/data2.json').read())
blank = json_utils.generate_dummy_json(schema)

diff = json_utils.diff_json(blank, result)
new_result = json_utils.set_history_item(
    blank,
    result,
    diff,
    {'point_id': 11, 'source': 'extractor', 'datetime': date_to_string(datetime.datetime.now())},
    copy.deepcopy(blank),
)
# print(json.dumps(new_result, indent=2))
diff = json_utils.diff_json(result, result2)
print(json.dumps(diff, indent=2))
new_result = json_utils.set_history_item(
    result,
    result2,
    diff,
    {'point_id': 11, 'source': 'change', 'datetime': date_to_string(datetime.datetime.now())},
    new_result,
)

print(json.dumps(new_result, indent=2))
