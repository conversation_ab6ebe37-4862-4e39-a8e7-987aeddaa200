aiofiles
alembic==1.15.2
asyncpg>=0.28.0
bcrypt==4.1.1
blowfish>=0.6.1

# Braintrust
autoevals>=0.0.107
braintrust>=0.0.174
braintrust_core>=0.0.54

cachetools>=5.3.1
docx2txt>=0.8
fastapi>=0.100.0
glymur

google-auth
google-auth-oauthlib
google-auth-httplib2
google-api-python-client

Jinja2>=3.1.6
jsondiff
jsf

imutils>=0.5.4

langchain==0.3.23
langchain-anthropic==0.3.10
langchain-community==0.3.21
langchain-core
langchain-experimental
langchain-google-genai==2.0.10
langchain-groq==0.2.1
langchain-mistralai==0.2.2
langchain-openai==0.3.12
langchain-qdrant==0.2.0
langchain-voyageai==0.1.4
langfuse==2.60.2
langgraph>=0.3.29
langgraph-checkpoint
markdownify>=1.1.0
matplotlib>=3.7.1
nemoguardrails>=0.10.1
networkx
opencv-python>=********
openpyxl>=3.1.5

pandas>=1.5.3
passlib==1.7.4
pdf2image>=1.17.0
Pillow>=10.0.1
plotly>=5.3.1
psycopg2-binary>=2.9.6
pydantic>=2.10.0
PyPDF2>=3.0.1
pymupdf4llm-kuberlab<0.0.17
pypdf>=3.13.0
pytesseract>=0.3.10
python-docx>=1.1.2
python-multipart>=0.0.19
python-ulid>=2.7.0
pyzbar>=0.1.9
# fixed ragas
git+https://github.com/nmakhotkin/ragas@0.2.15
rapidocr-onnxruntime
redis>=4.3.4
retry
scikit-learn>=1.2.2
scipy>=1.10.1
simsimd>=5.0.1
SQLAlchemy>=2.0.20
tenacity>=8.2.2
tiktoken>=0.7.0
unstructured>=0.17.2
uvicorn>=0.30.0
uvloop>=0.20.0
XlsxWriter
