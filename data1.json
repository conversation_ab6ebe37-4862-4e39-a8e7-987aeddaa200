{"accounting_method": "", "active_since": "", "agr_signed": "", "agreement_sum": 0, "billing_method": "", "bookkeeping": null, "name": "<PERSON><PERSON>", "company_phone": "", "cpa": null, "description": "qweqwewerww123412311232322322231223332", "dissolution_date": null, "ein": "", "fedtaxforms": "", "financial_year_end": "", "financial_year_end_for_subsidiary": null, "incorp_by": "IE", "legal_ent_type": null, "login": "", "monthly_bill": null, "naicscode": "", "notes_accounting": "", "notes_address": "", "notes_agreement": null, "notes_contacts": "", "notes_main": null, "notes_shareholders": "", "optional_share_count": 0, "paid_by": "", "paid_by_mail": null, "password": "", "payroll": null, "renewal_date": "", "renewal_date_mail": null, "since": null, "statetaxforms": "", "status": "hourly", "subjurisd": "", "subsidiary_legal_entity_type": "", "subsidiary_to_consolidate": "", "total_shares": 0, "withdrawal_date": null, "addresses": [{"id": "eac873bf-490a-4fa3-8b52-22290bf96e1e", "client_id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "address_type": "Foreign Address", "address_id": "a07e23a3-305e-427c-b2fa-ea9ae8d3fe8d", "renewal_date": null, "phone": null, "paid_by": "Self", "note": "dsfsdfwqe", "created_at": "2025-04-15 15:50:17", "updated_at": "2025-05-21 08:20:30", "address": {"id": "a07e23a3-305e-427c-b2fa-ea9ae8d3fe8d", "full_address": "233 Needham St, Suite 540 Newton MA, 02464", "street": "233 Needham St, Suite 540", "pobox": "", "city": "<PERSON>", "state": "MA", "zip": "02464", "country": "USA", "created_at": "2025-04-15 15:49:09", "updated_at": "2025-04-15 15:49:09"}}, {"address_type": "Foreign Address", "renewal_date": null, "phone": null, "paid_by": "Self", "note": "asdasdasdsdfsdfjhываываывававваысявввввааывваыв dfsdfsdfsdf", "address": {"id": "0e04e50e-1ce8-402b-b26d-e42c42314dce", "full_address": "233 Needham St, Suite 540 Newton MA, 02464", "street": "233 Needham St, Suite 540", "pobox": "", "city": "<PERSON>", "state": "MA", "zip": "02464", "country": "USA", "created_at": "2025-04-15 15:49:37", "updated_at": "2025-04-16 21:13:15"}, "id": "121a9508-3a92-4816-883d-524d3e909d27", "client_id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "address_id": "0e04e50e-1ce8-402b-b26d-e42c42314dce", "created_at": "2025-04-15 15:50:17", "updated_at": "2025-04-17 19:33:19"}], "authorized_signers": [], "bank_accounts": [{"id": "6e45c046-1c14-4328-b7b2-d25eeffc0d64", "client_id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "bank_name": "Bank Of America", "aba_number": "фывфывфыв", "account_number": null, "bank_contact": "", "controlled_by": null, "date_opened": null, "last_renewal": null, "notes": null, "authorized_signers": [], "bank_cards": []}], "contacts": [{"id": 1353, "client_id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "client_person_id": "0f131d2c-e90c-4787-9451-23ce5072917e", "position": "Manager", "email": null, "phone": null, "pcm": null, "note": null, "created_at": "2025-06-10 05:27:39", "updated_at": "2025-06-10 18:13:49", "person": {"id": "0f131d2c-e90c-4787-9451-23ce5072917e", "full_title": "<PERSON><PERSON> ", "firstname": "<PERSON><PERSON>", "lastname": "", "email": "", "phone": "", "pcm": "", "citizenship": "", "address": "", "companies": "", "created_at": "2025-03-06 21:13:38", "updated_at": "2025-03-06 21:13:38"}}], "payment_cards": [], "payment_services": [{"id": 25, "client_id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "payment_system": "BREX", "date_opened": null, "opened_by": null, "email_connected": null, "responsible_person": "we", "login_pass": null, "note": null}], "registrations": [{"id": 648, "client_id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "reg_agent_id": null, "is_primary": false, "reg_date": null, "deregister_date": null, "last_renewal_date": null, "reg_state": "IA", "reg_pay_by": null, "last_soi_filed": null, "state_entity": null, "notes": null, "created_at": "2025-05-14 16:40:03", "updated_at": "2025-05-21 08:18:18", "reg_agent": null}], "share_classes": [], "shareholders": [], "tax_reporting": [], "id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "internal_draft_flag": false, "manager_id": "dee6fb11-30ea-4f7d-8047-ef0837880eba", "source_id": "63572660-5a26-440e-a461-c22440ab48ae", "created_at": null, "updated_at": "2025-06-10 18:13:52", "services": [], "tasks": [], "manager": {"id": "dee6fb11-30ea-4f7d-8047-ef0837880eba", "user_id": 10, "title": "<PERSON><PERSON>", "email": "il<PERSON><PERSON><PERSON><PERSON>@gmail.com", "phone": null, "permissions": 1, "role_name": "owner", "person_id": null, "created_at": "2025-03-13 02:10:57", "updated_at": "2025-06-10 12:50:16"}, "source": {"id": "63572660-5a26-440e-a461-c22440ab48ae", "title": "<PERSON><PERSON>"}, "primary_registration": {"reg_date": null, "deregister_date": null, "last_renewal_date": null, "reg_state": "FL", "reg_pay_by": "", "last_soi_filed": null, "state_entity": "", "notes": null, "reg_agent": null, "id": 595, "client_id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "reg_agent_id": null, "is_primary": true, "created_at": "2025-04-16 20:49:20", "updated_at": "2025-06-04 05:36:33"}, "secondary_registrations": [{"id": 648, "client_id": "0f5dd30d-ff3a-4b7e-afca-700ff7eac315", "reg_agent_id": null, "is_primary": false, "reg_date": null, "deregister_date": null, "last_renewal_date": null, "reg_state": "IA", "reg_pay_by": null, "last_soi_filed": null, "state_entity": null, "notes": null, "created_at": "2025-05-14 16:40:03", "updated_at": "2025-05-21 08:18:18", "reg_agent": null}]}