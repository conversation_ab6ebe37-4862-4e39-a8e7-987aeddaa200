{"config": {"llm_type": "openai", "chunk_size": 1200, "chunker": "tiktoken", "chunk_overlap": 100, "prompt_config": {"metadata_prompt": "\nExtract comprehensive metadata from the document.\nFields to extract:\n- Name: Full title or name of the document.\n- Keywords: A list of relevant keywords.\n- Summary: A brief summary highlighting the document's key points.\n- Medication name (full product name)\n- Active ingredients and quantities\n- Pharmaceutical form\n- ATC (Anatomical Therapeutic Chemical) code and authorization numbers (CIP, CIS, EU etc.)\n- Special populations identifiers (pediatric, elderly, renal/hepatic impairment)\n- Standard dosage information and contraindications\n- Pharmaceutical information (excipients, shelf life, storage conditions)\n\nOutput must be in valid JSON format enclosed within triple backticks:\n```\n{\n  \"name\": \"document name\",\n  \"keywords\": [\"keyword1\", \"keyword2\"],\n  \"summary\": \"brief summary\",\n  \"medication_name\": \"medication name\",\n  \"active_ingredients\": [\"ingredient1 20mg\", \"ingredient2 500mg\"],\n  \"pharmaceutical_form\": \"form\",\n  \"atc_code\": \"ATC code\",\n  \"authorization_numbers\": [\"number1\", \"number2\"],\n  \"special_populations\": [\"pediatric\", \"elderly\"],\n  \"dosage_information\": \"dosage info\",\n  \"contraindications\": \"contraindications\",\n  \"pharmaceutical_information\": \"pharmaceutical info\"\n}\n``` \n", "chunk_metadata_prompt": "\nExtract specific metadata from this document chunk (a small section of a larger document).\nFields to extract:\n- Section: The specific section or topic this chunk covers.\n- Key_entities: Important people, organizations, or concepts mentioned in this chunk.\n- Context: How this chunk relates to the broader document or what it's part of.\n- Main_points: The key information or arguments presented in this chunk.\n- References: Any citations, figures, tables, or cross-references mentioned in this chunk.\n\nOutput must be in valid JSON format enclosed within triple backticks:\n```\n{\n  \"section\": \"section name\",\n  \"key_entities\": [\"entity1\", \"entity2\"],\n  \"context\": \"how this chunk fits into the document\",\n  \"main_points\": [\"point1\", \"point2\"],\n  \"references\": [\"reference1\", \"reference2\"],\n}\n```\n"}}}