api:
  Image:
    Name: kuberlab/ira-chat-api
    Tag: 1.3.43
  baseUrl: https://hotline.kibernetika.io
  db:
    dbname: ira
    enabled: true
    host: ira-chat-db
    password: Privet1961sneg
    user: postgres
  dns:
  - hotline.kibernetika.io
  envPrefix: 'prod'
  envVars:
  - name: dummy
    value: value
  - name: VECTORSTORE_ON_DISK
    value: "true"
  - name: LANGFUSE_SECRET_KEY
    value: "******************************************"
  - name: LANGFUSE_PUBLIC_KEY
    value: "pk-lf-6712fd7e-46d7-4e7e-a5e3-40681affb120"
  - name: LANGCHAIN_PROJECT
    value: "winpharma"
  - name: LANGCHAIN_API_KEY
    value: ***************************************************
  - name: LANGCHAIN_ENDPOINT
    value: "https://api.smith.langchain.com"
  redirectDomain: ''
  replicas: 6
credentials:
  admins: WzEsNCw1LDZdCg==
  smtp: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
crypto: jS2fKKFSlfco3mxLdfckd0ew
resources:
   limits:
     memory: 4Gi
   requests:
     memory: 1Gi
qdrant:
  nameOverride: "qdrant"
  fullnameOverride: "qdrant"
  resources:
    limits:
  #   cpu: 100m
      memory: 8Gi
    requests:
  #   cpu: 100m
      memory: 2Gi
  persistence:
    size: 50G

  image:
    repository: docker.io/qdrant/qdrant
    pullPolicy: IfNotPresent
    tag: "v1.10.1"

  # api key for authentication at qdrant
  # false: no api key will be configured
  # true: an api key will be auto-generated
  # string: the given string will be set as an apikey
  apiKey: false
persistence:
  db:
    size: 30G
    storageClass: standard
  files:
    size: 50G
    storageClass: standard
ui:
  Image:
    Name: kuberlab/ira-chat-ui
    Tag: 1.3.22
  replicas: 1
wildcard:
  dns: '*.hotline.kibernetika.io'
  enabled: true
  googleProject: lucid-timing-151005
  serviceAccountJson: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
