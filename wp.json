{"id": 35, "org_id": 7, "name": "WAP Assistance PROD", "display_name": "WAP-Assistance-PROD", "description": null, "created_at": "2025-05-01 20:34:54", "updated_at": "2025-05-01 20:55:58", "config": {"apps_config": {"chat": {"language": "english", "llm_type": "openai", "model_name": "gpt-4.1-mini", "vision_model": null, "temperature": "0.5", "index": "wp-test/openai", "chat_type": "agentic_rag", "prompt_config": {"system_prompt": "You are a highly capable and helpful AI assistant which called DoWAP.  You assist users in their questions about Winpharma software and tools, primarily WinAutopilote.\n\nYour primary goal is to assist users with accurate, informative, and relevant answers on their questions in a polite and approachable tone.\nYou are empathetic, friendly, and engaging, but you maintain a professional and focused demeanor to ensure clarity and precision.\n\nhe answer is going to be directly addressed to the user.\nDo not use any placeholders or generic sign-offs such as \"your name\" or similar. If you need to place a signature, sign as \"DoWAP Assistant\".\nIf the documentation is insufficient or unclear (or the question is unclear), ask for clarification.\nImportant: Instead of mentioning contacting the support in some cases, say that you can connect the user with the human technician if they need.\nUse the following documentation to answer the user question at the end.\n---\n{context}\n---\n", "condense_question_prompt": "Given a chat history and the latest user question which might reference context in the chat history, formulate a standalone question in its original language which can be understood without the chat history. Do NOT answer the question, just reformulate it if needed and otherwise return it as is."}, "input_rail_enabled": false, "output_rail_enabled": false, "output_check_enabled": true, "output_check_threshold": 4, "not_chat_topics": ["para<PERSON><PERSON><PERSON> mon <PERSON>", "<PERSON><PERSON>z vous me rappeler", "de nous recontacter nous", "de nous appeler svp", "serait til possible dorganiser un appel", "pouvez-vous nous appeler pour dépannage svp", "<PERSON><PERSON><PERSON> de me rappeler", "Facturation Paiements", "Gestion des factures", "Sérialisation Traçabilité", "Télécommunications teletransmission", "documents impression", "Inventaire Produits", "Sécurité Conformité", "Rendez-vous Planification", "Assistance client<PERSON><PERSON>"], "no_answer_template": "i-do-not-even-know-what-to-answer-to-this-44", "no_answer_strict": true, "search_k": 16, "not_chat_topics_score_threshold": 0.84, "available_tags": ["WAP", "Not WAP"], "show_metadata_keys": ["name"]}}}, "status": {"status": "SUCCESS", "message": null}, "access_type": "members", "user_permissions": ["owner", "all_chat.view", "file.manage", "workspace.manage", "group.manage", "members.manage", "metrics.manage", "all_chat.manage"]}