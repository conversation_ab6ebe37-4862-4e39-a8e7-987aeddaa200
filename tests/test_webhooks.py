"""
Tests for the webhooks service.
"""
import asyncio
import json
import pytest
import pytest_asyncio
import aiohttp
from aiohttp import web
from unittest.mock import AsyncMock, MagicMock, patch

from ira_chat.db import models
from ira_chat.services import webhooks


class TestWebhooks:
    """Tests for the webhooks service."""

    @pytest_asyncio.fixture
    async def test_server(self):
        """Create a test server for webhook testing."""
        # Store received requests for verification
        received_requests = []
        server_responses = []

        # Define request handler
        async def webhook_handler(request):
            # Read request body
            body = await request.read()
            headers = dict(request.headers)

            # Store request for later verification
            received_requests.append({
                "method": request.method,
                "path": request.path,
                "headers": headers,
                "body": body.decode('utf-8')
            })

            # Get the next response from the queue or use default
            if server_responses:
                status, body = server_responses.pop(0)
            else:
                status, body = 200, {"status": "ok"}

            # Return response
            return web.json_response(body, status=status)

        # Create and start server
        app = web.Application()
        app.router.add_post('/{tail:.*}', webhook_handler)

        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, 'localhost', 0)  # Use port 0 to get a random available port
        await site.start()

        # Get the assigned port
        server_port = site._server.sockets[0].getsockname()[1]
        server_url = f"http://localhost:{server_port}"

        # Return server info and control functions
        server_info = {
            "url": server_url,
            "requests": received_requests,
            "add_response": lambda status, body: server_responses.append((status, body)),
            "runner": runner
        }

        try:
            yield server_info
        finally:
            # Cleanup
            await runner.cleanup()

    @pytest_asyncio.fixture
    async def mock_webhook(self, test_server):
        """Create a mock webhook that points to our test server."""
        webhook = MagicMock(spec=models.OrganizationWebhook)
        webhook.id = 1
        webhook.key = "test_key"
        webhook.url = test_server["url"] + "/webhook"
        webhook.description = "Test webhook"
        webhook.config = {
            "organization": {"all": True},
            "workspace": {"all": True},
            "dataset": {"all": True}
        }
        webhook.get_headers.return_value = {"X-Test": "test"}
        return webhook

    @pytest_asyncio.fixture
    async def mock_org_sender(self):
        """Create a mock organization sender."""
        org = MagicMock(spec=models.Organization)
        org.id = 1
        return org

    @pytest_asyncio.fixture
    async def mock_workspace_sender(self):
        """Create a mock workspace sender."""
        workspace = MagicMock(spec=models.Workspace)
        workspace.id = 1
        return workspace

    @pytest_asyncio.fixture
    async def mock_db_api(self):
        """Mock the database API."""
        with patch("ira_chat.services.webhooks.db_api") as mock_db:
            mock_db.create_org_webhook_item = AsyncMock()
            mock_db.keep_n_webhook_items = AsyncMock()
            yield mock_db

    @pytest.mark.asyncio
    async def test_send_webhook_success(self, test_server, mock_webhook, mock_org_sender, mock_db_api):
        """Test successful webhook delivery."""
        # Data to send
        data = {"message": "Hello, webhook!"}

        # Set up server to return success
        test_server["add_response"](200, {"status": "success"})

        # Call the function
        await webhooks.send_webhook(mock_webhook, mock_org_sender, data)

        # Verify request was made to the server
        assert len(test_server["requests"]) == 1
        request = test_server["requests"][0]
        assert request["method"] == "POST"
        assert request["path"] == "/webhook"

        # Headers are converted to lowercase by aiohttp
        headers = {k.lower(): v for k, v in request["headers"].items()}
        assert "x-kibernetika-hmac-sha256" in headers
        assert "content-type" in headers
        assert json.loads(request["body"]) == data

        # Verify database calls
        mock_db_api.create_org_webhook_item.assert_called_once()
        webhook_item = mock_db_api.create_org_webhook_item.call_args[0][0]
        assert webhook_item["webhook_id"] == mock_webhook.id
        assert webhook_item["status_code"] == 200
        assert webhook_item["error"] is None
        mock_db_api.keep_n_webhook_items.assert_called_once_with(mock_webhook.id, 10)

    @pytest.mark.asyncio
    async def test_send_webhook_http_error(self, test_server, mock_webhook, mock_org_sender, mock_db_api):
        """Test webhook delivery with HTTP error."""
        # Data to send
        data = {"message": "Hello, webhook!"}

        # Set up server to return error for all requests (to test retry)
        for _ in range(10):  # More than the retry count
            test_server["add_response"](404, {"error": "Not Found"})

        # Mock the sleep function to speed up the test
        with patch("ira_chat.services.webhooks.asyncio.sleep", new_callable=AsyncMock):
            # Call the function
            await webhooks.send_webhook(mock_webhook, mock_org_sender, data)

        # Verify multiple requests were made to the server (retries)
        assert len(test_server["requests"]) > 1, "The webhook should have been retried"

        # Verify all requests had the correct format
        for request in test_server["requests"]:
            assert request["method"] == "POST"
            assert request["path"] == "/webhook"

            # Headers are converted to lowercase by aiohttp
            headers = {k.lower(): v for k, v in request["headers"].items()}
            assert "x-kibernetika-hmac-sha256" in headers
            assert "content-type" in headers
            assert json.loads(request["body"]) == data

        # Verify database calls
        mock_db_api.create_org_webhook_item.assert_called_once()
        webhook_item = mock_db_api.create_org_webhook_item.call_args[0][0]
        assert webhook_item["webhook_id"] == mock_webhook.id
        assert webhook_item["status_code"] == 404
        # The error message format can vary, but should contain the status code and some indication of the error
        assert "404:" in webhook_item["error"]
        assert "Not Found" in webhook_item["error"] or "error" in webhook_item["error"]
        mock_db_api.keep_n_webhook_items.assert_called_once_with(mock_webhook.id, 10)

    @pytest.mark.asyncio
    async def test_send_webhook_connection_error(self, test_server, mock_webhook, mock_org_sender, mock_db_api):
        """Test webhook delivery with connection error."""
        # Data to send
        data = {"message": "Hello, webhook!"}

        # Use an invalid URL to simulate connection error
        mock_webhook.url = "http://invalid-domain-that-does-not-exist.example"

        # Mock the sleep function to speed up the test
        with patch("ira_chat.services.webhooks.asyncio.sleep", new_callable=AsyncMock):
            # Call the function
            await webhooks.send_webhook(mock_webhook, mock_org_sender, data)

        # Verify database calls
        mock_db_api.create_org_webhook_item.assert_called_once()
        webhook_item = mock_db_api.create_org_webhook_item.call_args[0][0]
        assert webhook_item["webhook_id"] == mock_webhook.id
        assert webhook_item["status_code"] == 0
        # The exact error message can vary by platform, so we check for common substrings
        error_msg = webhook_item["error"]
        assert any(substr in error_msg for substr in ["ConnectError", "Connection", "DNS", "Name or service", "Temporary failure"])
        mock_db_api.keep_n_webhook_items.assert_called_once_with(mock_webhook.id, 10)

    @pytest.mark.asyncio
    async def test_send_webhook_retry_mechanism(self, test_server, mock_webhook, mock_org_sender, mock_db_api):
        """Test webhook retry mechanism."""
        # Data to send
        data = {"message": "Hello, webhook!"}

        # Set up server to fail twice and then succeed
        test_server["add_response"](500, {"error": "Server Error"})
        test_server["add_response"](500, {"error": "Server Error"})
        test_server["add_response"](200, {"status": "success"})

        # Patch sleep to make the test run faster
        with patch("ira_chat.services.webhooks.asyncio.sleep", new_callable=AsyncMock) as mock_sleep:
            # Call the function
            await webhooks.send_webhook(mock_webhook, mock_org_sender, data)

            # Verify sleep was called between retries
            assert mock_sleep.call_count == 2, "Sleep should have been called between retries"

        # Verify exactly 3 requests were made (2 failures + 1 success)
        assert len(test_server["requests"]) == 3, "The webhook should have been retried exactly twice"

        # Verify database calls
        mock_db_api.create_org_webhook_item.assert_called_once()
        webhook_item = mock_db_api.create_org_webhook_item.call_args[0][0]
        assert webhook_item["webhook_id"] == mock_webhook.id
        assert webhook_item["status_code"] == 200
        assert webhook_item["error"] is None
        mock_db_api.keep_n_webhook_items.assert_called_once_with(mock_webhook.id, 10)

    @pytest.mark.asyncio
    async def test_webhook_not_sent_due_to_config(self, test_server, mock_webhook, mock_workspace_sender, mock_db_api):
        """Test webhook not sent due to configuration."""
        # Configure webhook to only accept specific workspace IDs
        mock_webhook.config = {
            "organization": {"all": True},
            "workspace": {"all": False, "ids": [2, 3]},  # Not including our test workspace ID (1)
            "dataset": {"all": True}
        }

        # Mock data to send
        data = {"message": "Hello, webhook!"}

        # Call the function
        await webhooks.send_webhook(mock_webhook, mock_workspace_sender, data)

        # Verify no requests were made to the server
        assert len(test_server["requests"]) == 0

        # Verify database calls were NOT made
        mock_db_api.create_org_webhook_item.assert_not_called()
        mock_db_api.keep_n_webhook_items.assert_not_called()

    @pytest.mark.asyncio
    async def test_hmac_signature_generation(self, test_server, mock_webhook, mock_org_sender, mock_db_api):
        """Test HMAC signature generation."""
        # Data to send
        data = {"message": "Hello, webhook!"}
        data_str = json.dumps(data)

        # Calculate expected HMAC
        import base64
        import hmac
        import hashlib
        expected_hmac = base64.standard_b64encode(
            hmac.new(
                mock_webhook.key.encode(),
                data_str.encode(),
                hashlib.sha256
            ).digest()
        ).decode()

        # Set up server to return success
        test_server["add_response"](200, {"status": "success"})

        # Call the function
        await webhooks.send_webhook(mock_webhook, mock_org_sender, data)

        # Verify request was made with correct HMAC
        assert len(test_server["requests"]) == 1
        request = test_server["requests"][0]

        # Headers are converted to lowercase by aiohttp
        headers = {k.lower(): v for k, v in request["headers"].items()}
        assert "x-kibernetika-hmac-sha256" in headers
        assert headers["x-kibernetika-hmac-sha256"] == expected_hmac
